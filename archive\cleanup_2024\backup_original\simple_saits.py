"""
Simplified SAITS implementation for well log prediction.
This version focuses on robustness and compatibility with the existing ML framework.
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler
from sklearn.base import BaseEstimator, RegressorMixin
import warnings
warnings.filterwarnings('ignore')


class SimpleSAITSRegressor(BaseEstimator, RegressorMixin):
    """
    Simplified SAITS Regressor for well log prediction.
    
    This is a more robust implementation that focuses on the core SAITS concepts
    while being compatible with the existing ML framework.
    """
    
    def __init__(self, sequence_length=50, d_model=64, n_head=4, n_layers=2,
                 dropout=0.1, epochs=100, batch_size=32, learning_rate=0.001,
                 patience=10, random_state=42, **kwargs):
        
        self.sequence_length = sequence_length
        self.d_model = d_model
        self.n_head = n_head
        self.n_layers = n_layers
        self.dropout = dropout
        self.epochs = epochs
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.patience = patience
        self.random_state = random_state
        
        # Set device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize components
        self.model = None
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        self.feature_names_ = None
        
        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)
    
    def _create_sequences(self, X, y=None):
        """Convert tabular data to sequences."""
        sequences_X = []
        sequences_y = []
        
        n_samples = len(X)
        
        for i in range(n_samples - self.sequence_length + 1):
            seq_X = X[i:i + self.sequence_length]
            sequences_X.append(seq_X)
            
            if y is not None:
                # For target, we predict the last value in the sequence
                seq_y = y[i + self.sequence_length - 1]
                sequences_y.append(seq_y)
        
        sequences_X = np.array(sequences_X)
        
        if y is not None:
            sequences_y = np.array(sequences_y)
            return sequences_X, sequences_y
        else:
            return sequences_X
    
    def _prepare_data(self, X, y=None, fit_scalers=False):
        """Prepare data for training/prediction."""
        X_array = np.array(X)
        
        # Handle missing values by forward filling then backward filling
        X_df = pd.DataFrame(X_array)
        X_filled = X_df.fillna(method='ffill').fillna(method='bfill').fillna(0).values
        
        if fit_scalers:
            X_scaled = self.scaler_X.fit_transform(X_filled)
            if y is not None:
                y_array = np.array(y)
                y_filled = pd.Series(y_array).fillna(method='ffill').fillna(method='bfill').fillna(0).values
                y_scaled = self.scaler_y.fit_transform(y_filled.reshape(-1, 1)).flatten()
        else:
            X_scaled = self.scaler_X.transform(X_filled)
            if y is not None:
                y_array = np.array(y)
                y_filled = pd.Series(y_array).fillna(method='ffill').fillna(method='bfill').fillna(0).values
                y_scaled = self.scaler_y.transform(y_filled.reshape(-1, 1)).flatten()
        
        if y is not None:
            return X_scaled, y_scaled
        else:
            return X_scaled
    
    def fit(self, X, y, eval_set=None, verbose=False):
        """Fit the simplified SAITS model."""
        self.feature_names_ = getattr(X, 'columns', None)
        
        # Prepare data
        X_prep, y_prep = self._prepare_data(X, y, fit_scalers=True)
        
        # Create sequences
        X_seq, y_seq = self._create_sequences(X_prep, y_prep)
        
        if len(X_seq) == 0:
            raise ValueError(f"Not enough data to create sequences. Need at least {self.sequence_length} samples.")
        
        # Initialize model
        n_features = X_seq.shape[2]
        self.model = SimpleSAITSModel(
            n_features=n_features,
            d_model=self.d_model,
            n_head=self.n_head,
            n_layers=self.n_layers,
            dropout=self.dropout
        ).to(self.device)
        
        # Setup optimizer
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.learning_rate)
        criterion = nn.MSELoss()
        
        # Training loop
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(self.epochs):
            epoch_loss = 0
            n_batches = 0
            
            # Simple batch processing
            for i in range(0, len(X_seq), self.batch_size):
                batch_X = X_seq[i:i + self.batch_size]
                batch_y = y_seq[i:i + self.batch_size]
                
                # Convert to tensors
                X_tensor = torch.FloatTensor(batch_X).to(self.device)
                y_tensor = torch.FloatTensor(batch_y).to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(X_tensor)
                loss = criterion(outputs.squeeze(), y_tensor)
                
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                n_batches += 1
            
            avg_loss = epoch_loss / n_batches if n_batches > 0 else float('inf')
            
            if verbose and epoch % 10 == 0:
                print(f"Epoch {epoch}, Loss: {avg_loss:.4f}")
            
            # Early stopping
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
            else:
                patience_counter += 1
                if patience_counter >= self.patience:
                    if verbose:
                        print(f"Early stopping at epoch {epoch}")
                    break
        
        return self
    
    def predict(self, X):
        """Predict using the trained model."""
        if self.model is None:
            raise ValueError("Model must be fitted before prediction")
        
        # Prepare data
        X_prep = self._prepare_data(X, fit_scalers=False)
        
        # Create sequences
        X_seq = self._create_sequences(X_prep)
        
        if len(X_seq) == 0:
            # If we can't create sequences, return NaN for all predictions
            return np.full(len(X), np.nan)
        
        self.model.eval()
        predictions = []
        
        with torch.no_grad():
            for i in range(0, len(X_seq), self.batch_size):
                batch_X = X_seq[i:i + self.batch_size]
                
                # Convert to tensors
                X_tensor = torch.FloatTensor(batch_X).to(self.device)
                
                outputs = self.model(X_tensor)
                batch_preds = outputs.squeeze().cpu().numpy()
                
                if batch_preds.ndim == 0:
                    batch_preds = [batch_preds]
                
                predictions.extend(batch_preds)
        
        # Convert back to original scale
        predictions = np.array(predictions).reshape(-1, 1)
        predictions_scaled = self.scaler_y.inverse_transform(predictions).flatten()
        
        # Handle the sequence length offset
        full_predictions = np.full(len(X), np.nan)
        full_predictions[self.sequence_length-1:self.sequence_length-1+len(predictions_scaled)] = predictions_scaled
        
        return full_predictions
    
    def get_params(self, deep=True):
        """Get parameters for this estimator."""
        return {
            'sequence_length': self.sequence_length,
            'd_model': self.d_model,
            'n_head': self.n_head,
            'n_layers': self.n_layers,
            'dropout': self.dropout,
            'epochs': self.epochs,
            'batch_size': self.batch_size,
            'learning_rate': self.learning_rate,
            'patience': self.patience,
            'random_state': self.random_state
        }
    
    def set_params(self, **params):
        """Set parameters for this estimator."""
        for key, value in params.items():
            setattr(self, key, value)
        return self


class SimpleSAITSModel(nn.Module):
    """Simplified SAITS model using standard PyTorch components."""
    
    def __init__(self, n_features, d_model=64, n_head=4, n_layers=2, dropout=0.1):
        super().__init__()
        
        self.n_features = n_features
        self.d_model = d_model
        
        # Input projection
        self.input_projection = nn.Linear(n_features, d_model)
        
        # Positional encoding
        self.pos_encoding = nn.Parameter(torch.randn(1000, d_model))
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_head,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # Output projection
        self.output_projection = nn.Linear(d_model, 1)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        # x shape: (batch_size, seq_len, n_features)
        batch_size, seq_len, _ = x.shape
        
        # Project input to model dimension
        x = self.input_projection(x)
        
        # Add positional encoding
        pos_enc = self.pos_encoding[:seq_len, :].unsqueeze(0).expand(batch_size, -1, -1)
        x = x + pos_enc
        
        # Apply dropout
        x = self.dropout(x)
        
        # Apply transformer
        x = self.transformer(x)
        
        # Take the last timestep and project to output
        x = x[:, -1, :]  # (batch_size, d_model)
        output = self.output_projection(x)  # (batch_size, 1)
        
        return output
