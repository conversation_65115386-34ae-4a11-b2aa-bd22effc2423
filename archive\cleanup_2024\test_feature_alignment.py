#!/usr/bin/env python3
"""
Test script to verify feature alignment fixes in the ML log prediction pipeline.
This script tests that all models can successfully generate predictions with consistent feature sets.
"""

import numpy as np
import pandas as pd
import sys
import os

# Add the current directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ml_core import impute_logs, MODEL_REGISTRY
from reporting import create_all_models_plots

def create_test_data_with_extra_features():
    """Create test data that simulates the real scenario with 41 features but only 5 should be used."""
    np.random.seed(42)
    n_samples = 1000
    
    # Core 5 features that should be used for training
    core_features = ['GR', 'NPHI', 'RHOB', 'RT', 'MD']
    
    # Additional 36 features that exist in data but should NOT be used for training
    extra_features = [
        'FACIES_INTERPRETATION_2021', 'FLUID_CODE', 'P-IMPEDANCE_TRANS',
        'S-IMPEDANCE_TRANS', 'DENSITY_TRANS', 'POROSITY_TRANS',
        'WATER_SATURATION', 'OIL_SATURATION', 'GAS_SATURATION',
        'PERMEABILITY_X', 'PERMEABILITY_Y', 'PERMEABILITY_Z',
        'YOUNG_MODULUS', 'POISSON_RATIO', 'BULK_MODULUS',
        'SHEAR_MODULUS', 'BRITTLENESS_INDEX', 'CLAY_VOLUME',
        'QUARTZ_VOLUME', 'CALCITE_VOLUME', 'DOLOMITE_VOLUME',
        'PYRITE_VOLUME', 'KEROGEN_VOLUME', 'TOTAL_ORGANIC_CARBON',
        'VITRINITE_REFLECTANCE', 'THERMAL_MATURITY', 'FRACTURE_PRESSURE',
        'PORE_PRESSURE', 'OVERBURDEN_PRESSURE', 'EFFECTIVE_STRESS',
        'MINIMUM_HORIZONTAL_STRESS', 'MAXIMUM_HORIZONTAL_STRESS',
        'STRESS_RATIO', 'FRACTURE_GRADIENT', 'MUD_WEIGHT',
        'DRILLING_RATE'
    ]
    
    # Create DataFrame with all features
    data = {}
    
    # Add core features with realistic well log values
    data['GR'] = np.random.normal(80, 30, n_samples)  # Gamma Ray
    data['NPHI'] = np.random.beta(2, 5, n_samples)    # Neutron Porosity
    data['RHOB'] = np.random.normal(2.3, 0.2, n_samples)  # Bulk Density
    data['RT'] = np.random.lognormal(1, 1, n_samples)  # Resistivity
    data['MD'] = np.linspace(1000, 3000, n_samples)    # Measured Depth
    
    # Add extra features (these should NOT be used for training)
    for feature in extra_features:
        data[feature] = np.random.normal(0, 1, n_samples)
    
    # Create target variable (DT - Delta Time) based on core features
    data['DT'] = (100 + 
                  0.3 * data['GR'] + 
                  50 * data['NPHI'] - 
                  20 * data['RHOB'] + 
                  np.log(data['RT']) * 5 + 
                  np.random.normal(0, 5, n_samples))
    
    # Add well information
    data['WELL'] = ['WELL_A'] * (n_samples // 2) + ['WELL_B'] * (n_samples - n_samples // 2)
    
    df = pd.DataFrame(data)
    
    # Introduce some missing values in target to simulate real scenario
    missing_indices = np.random.choice(df.index, size=int(0.3 * len(df)), replace=False)
    df.loc[missing_indices, 'DT'] = np.nan
    
    return df, core_features

def test_feature_alignment():
    """Test that feature alignment works correctly."""
    print("🧪 Testing Feature Alignment Fixes")
    print("=" * 50)
    
    # Create test data
    print("📊 Creating test data with 41 features (only 5 should be used)...")
    df, expected_features = create_test_data_with_extra_features()
    
    print(f"✅ Created test data:")
    print(f"   • Total features in data: {len(df.columns) - 2}")  # Exclude WELL and target
    print(f"   • Expected training features: {expected_features}")
    print(f"   • Extra features that should be ignored: {len(df.columns) - len(expected_features) - 2}")
    
    # Configure test parameters
    feature_cols = expected_features[:-1]  # Exclude MD as it's added automatically
    target_col = 'DT'
    well_cfg = {'mode': 'mixed', 'training_wells': ['WELL_A', 'WELL_B'], 'prediction_wells': ['WELL_A', 'WELL_B']}
    prediction_mode = 1
    
    # Create models (use a subset for faster testing)
    test_models = {}
    for model_key in ['xgboost', 'lightgbm', 'catboost']:
        if model_key in MODEL_REGISTRY:
            model_class = MODEL_REGISTRY[model_key]['model_class']
            # Use simple hyperparameters for testing
            if model_key == 'xgboost':
                test_models[MODEL_REGISTRY[model_key]['name']] = model_class(n_estimators=50, random_state=42)
            elif model_key == 'lightgbm':
                test_models[MODEL_REGISTRY[model_key]['name']] = model_class(n_estimators=50, random_state=42, verbose=-1)
            elif model_key == 'catboost':
                test_models[MODEL_REGISTRY[model_key]['name']] = model_class(iterations=50, random_state=42, verbose=False)
    
    print(f"\n🤖 Testing {len(test_models)} models: {list(test_models.keys())}")
    
    # Test the main imputation function
    print("\n🔧 Running impute_logs function...")
    try:
        res_df, model_results = impute_logs(df, feature_cols, target_col, test_models, well_cfg, prediction_mode)
        
        if not model_results:
            print("❌ Model training failed")
            return False
            
        print("✅ impute_logs completed successfully")
        print(f"   • Best model: {model_results['best_model_name']}")
        print(f"   • Feature columns stored: {model_results.get('feature_cols', 'NOT FOUND')}")
        
        # Verify that the correct features were used
        stored_features = model_results.get('feature_cols', [])
        if stored_features == feature_cols:
            print("✅ Feature columns correctly stored in model results")
        else:
            print(f"❌ Feature mismatch in stored results: expected {feature_cols}, got {stored_features}")
            return False
            
    except Exception as e:
        print(f"❌ Error in impute_logs: {e}")
        return False
    
    # Test the reporting function (this was the main source of the bug)
    print("\n📊 Testing create_all_models_plots function...")
    try:
        create_all_models_plots(res_df, model_results, well_cfg)
        print("✅ create_all_models_plots completed successfully")
        print("✅ All models generated predictions without feature mismatch errors")
        
    except Exception as e:
        print(f"❌ Error in create_all_models_plots: {e}")
        return False
    
    # Verify predictions were generated
    target_pred_col = f"{target_col}_pred"
    if target_pred_col in res_df.columns:
        pred_count = res_df[target_pred_col].notna().sum()
        print(f"✅ Generated {pred_count} predictions")
    else:
        print("❌ No predictions column found")
        return False
    
    print("\n🎉 All tests passed! Feature alignment is working correctly.")
    return True

def test_saits_models():
    """Test SAITS models if available."""
    print("\n🔬 Testing SAITS Models...")
    
    try:
        from ml_core import SAITS_AVAILABLE, SIMPLE_SAITS_AVAILABLE
        
        if not SAITS_AVAILABLE and not SIMPLE_SAITS_AVAILABLE:
            print("⚠️  SAITS models not available - skipping SAITS tests")
            return True
            
        # Create smaller test data for SAITS (they need sequence data)
        df, expected_features = create_test_data_with_extra_features()
        df = df.head(200)  # Smaller dataset for SAITS testing
        
        feature_cols = expected_features[:-1]  # Exclude MD
        target_col = 'DT'
        well_cfg = {'mode': 'mixed', 'training_wells': ['WELL_A', 'WELL_B'], 'prediction_wells': ['WELL_A', 'WELL_B']}
        prediction_mode = 1
        
        # Test SAITS models
        saits_models = {}
        if SAITS_AVAILABLE:
            from ml_core import SAITSRegressor
            saits_models['SAITS'] = SAITSRegressor(sequence_length=20, epochs=5)
            
        if SIMPLE_SAITS_AVAILABLE:
            from ml_core import SimpleSAITSRegressor
            saits_models['Simple SAITS'] = SimpleSAITSRegressor(sequence_length=20, epochs=5)
        
        if not saits_models:
            print("⚠️  No SAITS models available for testing")
            return True
            
        print(f"🧪 Testing {len(saits_models)} SAITS models: {list(saits_models.keys())}")
        
        # Test SAITS models
        res_df, model_results = impute_logs(df, feature_cols, target_col, saits_models, well_cfg, prediction_mode)
        
        if model_results:
            print("✅ SAITS models completed successfully")
            print(f"   • Best SAITS model: {model_results['best_model_name']}")
            
            # Test plotting with SAITS
            create_all_models_plots(res_df, model_results, well_cfg)
            print("✅ SAITS models plotting completed successfully")
        else:
            print("⚠️  SAITS models training failed (this may be expected for small test data)")
            
    except Exception as e:
        print(f"⚠️  SAITS testing failed: {e}")
        print("   This may be expected if SAITS dependencies are not properly configured")
        
    return True

if __name__ == "__main__":
    print("🚀 Starting Feature Alignment Test Suite")
    print("=" * 60)
    
    success = True
    
    # Test main models
    success &= test_feature_alignment()
    
    # Test SAITS models
    success &= test_saits_models()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED! Feature alignment fixes are working correctly.")
        print("\n📋 Summary of fixes:")
        print("   ✅ Training uses configured feature set (5 features)")
        print("   ✅ Prediction uses same feature set as training")
        print("   ✅ Feature validation prevents mismatches")
        print("   ✅ All model types work with consistent features")
    else:
        print("❌ SOME TESTS FAILED! Please check the error messages above.")
        sys.exit(1)
