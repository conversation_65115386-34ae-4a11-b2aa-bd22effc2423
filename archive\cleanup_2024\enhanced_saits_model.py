"""
Enhanced SAITS Model with Comprehensive NaN Handling and GPU Optimization
=========================================================================

This module provides an enhanced SAITS implementation that integrates:
1. Comprehensive NaN preprocessing
2. GPU optimization
3. Performance monitoring
4. Robust error handling

This resolves the "Input contains NaN" error and provides optimal performance.
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.base import BaseEstimator, RegressorMixin
import warnings
warnings.filterwarnings('ignore')

# Import our enhanced modules
try:
    from saits_preprocessing import SAITSPreprocessor
    from saits_gpu_performance import SAITSGPUOptimizer
    from saits_monitoring import SAITSMonitor
    from saits_model import SAITS  # Import the core SAITS model
    ENHANCED_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Enhanced modules not available - {e}")
    ENHANCED_MODULES_AVAILABLE = False


class EnhancedSAITSRegressor(BaseEstimator, RegressorMixin):
    """
    Enhanced SAITS Regressor with comprehensive NaN handling and optimization.

    This class provides a robust, production-ready SAITS implementation that:
    - Automatically handles NaN values without errors
    - Optimizes GPU usage and performance
    - Provides comprehensive monitoring and logging
    - Includes robust error handling and recovery
    """

    def __init__(self,
                 # Core SAITS parameters
                 sequence_length: int = 50,
                 n_groups: int = 1,
                 n_group_inner_layers: int = 1,
                 d_model: int = 64,
                 d_inner: int = 128,
                 n_head: int = 4,
                 d_k: int = 16,
                 d_v: int = 16,
                 dropout: float = 0.1,
                 epochs: int = 100,
                 batch_size: int = 32,
                 learning_rate: float = 0.001,
                 patience: int = 10,

                 # Enhanced features
                 enable_preprocessing: bool = True,
                 enable_gpu_optimization: bool = True,
                 enable_monitoring: bool = True,
                 normalization_method: str = 'standard',
                 handle_outliers: bool = True,
                 enable_mixed_precision: bool = True,

                 # Legacy parameters for compatibility
                 input_with_mask: bool = True,
                 param_sharing_strategy: str = "inner_group",
                 MIT: bool = False,
                 random_state: int = 42,
                 **kwargs):
        """
        Initialize Enhanced SAITS Regressor.

        Args:
            sequence_length: Length of input sequences
            n_groups: Number of attention groups
            n_group_inner_layers: Layers per group
            d_model: Model dimension
            d_inner: Inner dimension
            n_head: Number of attention heads
            d_k: Key dimension
            d_v: Value dimension
            dropout: Dropout rate
            epochs: Training epochs
            batch_size: Batch size
            learning_rate: Learning rate
            patience: Early stopping patience
            enable_preprocessing: Enable enhanced preprocessing
            enable_gpu_optimization: Enable GPU optimization
            enable_monitoring: Enable performance monitoring
            normalization_method: 'standard' or 'robust'
            handle_outliers: Whether to handle outliers
            enable_mixed_precision: Enable mixed precision training
            input_with_mask: Use input with mask (legacy)
            param_sharing_strategy: Parameter sharing strategy (legacy)
            MIT: Use MIT loss (legacy)
            random_state: Random seed
        """
        # Store all parameters
        self.sequence_length = sequence_length
        self.n_groups = n_groups
        self.n_group_inner_layers = n_group_inner_layers
        self.d_model = d_model
        self.d_inner = d_inner
        self.n_head = n_head
        self.d_k = d_k
        self.d_v = d_v
        self.dropout = dropout
        self.epochs = epochs
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.patience = patience

        # Enhanced features
        self.enable_preprocessing = enable_preprocessing
        self.enable_gpu_optimization = enable_gpu_optimization
        self.enable_monitoring = enable_monitoring
        self.normalization_method = normalization_method
        self.handle_outliers = handle_outliers
        self.enable_mixed_precision = enable_mixed_precision

        # Legacy parameters
        self.input_with_mask = input_with_mask
        self.param_sharing_strategy = param_sharing_strategy
        self.MIT = MIT
        self.random_state = random_state

        # Initialize components
        self.model = None
        self.preprocessor = None
        self.gpu_optimizer = None
        self.monitor = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)

        # Initialize enhanced components if available
        if ENHANCED_MODULES_AVAILABLE:
            self._initialize_enhanced_components()
        else:
            print("⚠️ Enhanced modules not available - using basic functionality")

    def _initialize_enhanced_components(self):
        """Initialize enhanced preprocessing, GPU optimization, and monitoring."""
        try:
            # Initialize preprocessor
            if self.enable_preprocessing:
                self.preprocessor = SAITSPreprocessor(
                    normalization_method=self.normalization_method,
                    handle_outliers=self.handle_outliers,
                    device='auto'
                )
                print("✅ Enhanced preprocessing initialized")

            # Initialize GPU optimizer
            if self.enable_gpu_optimization:
                self.gpu_optimizer = SAITSGPUOptimizer(
                    enable_mixed_precision=self.enable_mixed_precision
                )
                print("✅ GPU optimization initialized")

            # Initialize monitor
            if self.enable_monitoring:
                self.monitor = SAITSMonitor(
                    log_file="enhanced_saits_training.log",
                    save_plots=True
                )
                print("✅ Performance monitoring initialized")

        except Exception as e:
            print(f"⚠️ Warning: Could not initialize enhanced components - {e}")
            self.enable_preprocessing = False
            self.enable_gpu_optimization = False
            self.enable_monitoring = False