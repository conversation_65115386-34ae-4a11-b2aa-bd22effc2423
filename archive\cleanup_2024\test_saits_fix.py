#!/usr/bin/env python3
"""
Test script to verify the SimpleSAITSRegressor TypeError fix.
"""

import numpy as np
import sys
import traceback

def test_simple_saits_instantiation():
    """Test that SimpleSAITSRegressor can be instantiated with d_inner parameter."""
    print("🧪 Testing SimpleSAITSRegressor instantiation with d_inner parameter...")
    
    try:
        # Import the fixed SimpleSAITSRegressor
        from simple_saits import SimpleSAITSRegressor
        
        # Test 1: Instantiate without d_inner (should work)
        print("   Test 1: Instantiate without d_inner...")
        model1 = SimpleSAITSRegressor(
            sequence_length=30,
            d_model=32,
            n_head=2,
            epochs=1,
            batch_size=8
        )
        print("   ✅ Test 1 passed: SimpleSAITSRegressor instantiated without d_inner")
        
        # Test 2: Instantiate with d_inner (should work now)
        print("   Test 2: Instantiate with d_inner...")
        model2 = SimpleSAITSRegressor(
            sequence_length=30,
            d_model=32,
            n_head=2,
            epochs=1,
            batch_size=8,
            d_inner=64  # This should now be accepted
        )
        print("   ✅ Test 2 passed: SimpleSAITSRegressor instantiated with d_inner")
        
        # Test 3: Test with hyperparameters from GPU optimizer
        print("   Test 3: Test with GPU optimizer hyperparameters...")
        from saits_gpu_optimizer import SAITSGPUOptimizer
        
        optimizer = SAITSGPUOptimizer()
        base_params = {
            'sequence_length': 50,
            'd_model': 64,
            'n_head': 4,
            'batch_size': 32,
            'epochs': 10
        }
        
        # Test with simple_saits model type (should not add d_inner)
        optimized_params = optimizer.optimize_hyperparameters_for_gpu(base_params, model_type='simple_saits')
        print(f"   Optimized params for simple_saits: {optimized_params}")
        
        model3 = SimpleSAITSRegressor(**optimized_params)
        print("   ✅ Test 3 passed: SimpleSAITSRegressor instantiated with GPU-optimized params")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        traceback.print_exc()
        return False

def test_model_registry_compatibility():
    """Test that the MODEL_REGISTRY works with the fixed SimpleSAITSRegressor."""
    print("\n🧪 Testing MODEL_REGISTRY compatibility...")
    
    try:
        from ml_core import MODEL_REGISTRY
        from config_handler import configure_hyperparameters
        
        # Get hyperparameters
        hparams = configure_hyperparameters()
        
        # Test instantiation of simple_saits model if available
        if 'simple_saits' in MODEL_REGISTRY:
            print("   Testing simple_saits model from registry...")
            model_config = MODEL_REGISTRY['simple_saits']
            model_class = model_config['model_class']
            model_hparams = hparams['simple_saits']
            
            # Add d_inner to test compatibility
            test_hparams = model_hparams.copy()
            test_hparams['d_inner'] = 128
            
            model = model_class(**test_hparams)
            print("   ✅ simple_saits model instantiated successfully from registry")
            
        else:
            print("   ⚠️  simple_saits not found in MODEL_REGISTRY")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Registry test failed: {e}")
        traceback.print_exc()
        return False

def test_gpu_optimizer_integration():
    """Test the GPU optimizer integration with the fix."""
    print("\n🧪 Testing GPU optimizer integration...")
    
    try:
        from main import optimize_saits_hyperparameters
        from config_handler import configure_hyperparameters
        
        # Get base hyperparameters
        hparams = configure_hyperparameters()
        
        # Test GPU optimization (even if GPU is not available)
        optimized_hparams = optimize_saits_hyperparameters(hparams, gpu_enabled=True)
        
        # Try to instantiate models with optimized hyperparameters
        if 'simple_saits' in optimized_hparams:
            from simple_saits import SimpleSAITSRegressor
            model = SimpleSAITSRegressor(**optimized_hparams['simple_saits'])
            print("   ✅ SimpleSAITSRegressor instantiated with GPU-optimized hyperparameters")
        
        return True
        
    except Exception as e:
        print(f"   ❌ GPU optimizer integration test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Testing SimpleSAITSRegressor TypeError Fix")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test 1: Basic instantiation
    if not test_simple_saits_instantiation():
        all_tests_passed = False
    
    # Test 2: MODEL_REGISTRY compatibility
    if not test_model_registry_compatibility():
        all_tests_passed = False
    
    # Test 3: GPU optimizer integration
    if not test_gpu_optimizer_integration():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 All tests passed! The TypeError fix is working correctly.")
        print("✅ SimpleSAITSRegressor now accepts the 'd_inner' parameter for compatibility.")
        return 0
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
