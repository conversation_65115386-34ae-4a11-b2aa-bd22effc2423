#!/usr/bin/env python3
"""
Test script to validate SAITS fixes for:
1. Prediction length mismatch error
2. Performance optimization
"""

import numpy as np
import pandas as pd
import sys
import os

# Add current directory to path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from saits_model_fixed import SAITSRegressor
    from ml_core import MODEL_REGISTRY
    print("✅ Successfully imported SAITS modules")
except ImportError as e:
    print(f"❌ Failed to import SAITS modules: {e}")
    sys.exit(1)

def test_performance_optimization():
    """Test that the new hyperparameters are correctly configured."""
    print("\n🔧 Testing Performance Optimization...")
    
    if 'saits' not in MODEL_REGISTRY:
        print("❌ SAITS not found in MODEL_REGISTRY")
        return False
    
    saits_config = MODEL_REGISTRY['saits']['hyperparameters']
    
    # Check optimized parameters
    expected_params = {
        'sequence_length': 40,
        'batch_size': 64,
        'd_model': 96,
        'd_inner': 192
    }
    
    all_correct = True
    for param, expected_value in expected_params.items():
        actual_value = saits_config[param]['default']
        if actual_value == expected_value:
            print(f"   ✅ {param}: {actual_value} (optimized)")
        else:
            print(f"   ❌ {param}: {actual_value}, expected {expected_value}")
            all_correct = False
    
    return all_correct

def test_prediction_length_handling():
    """Test that SAITS prediction length mismatch is handled correctly."""
    print("\n🔮 Testing Prediction Length Handling...")
    
    try:
        # Create synthetic data
        np.random.seed(42)
        n_samples = 100
        n_features = 5
        
        X = np.random.randn(n_samples, n_features)
        y = np.random.randn(n_samples)
        
        # Add some NaN values to simulate real well log data
        X[::10, 0] = np.nan
        y[::15] = np.nan
        
        print(f"   📊 Created test data: X={X.shape}, y={y.shape}")
        
        # Initialize SAITS with optimized parameters
        model = SAITSRegressor(
            sequence_length=40,
            epochs=2,  # Quick test
            batch_size=64,
            d_model=96,
            d_inner=192,
            n_head=4,
            verbose=False
        )
        
        print("   🏋️  Training SAITS model...")
        model.fit(X, y)
        
        print("   🔮 Testing prediction...")
        predictions = model.predict(X)
        
        print(f"   📏 Input length: {len(X)}")
        print(f"   📏 Prediction length: {len(predictions)}")
        
        # Test the length handling logic (simulate what happens in reporting.py)
        res_df = pd.DataFrame({'index': range(len(X))})
        pred_mask = pd.Series(True, index=res_df.index)
        full_pred = pd.Series(np.nan, index=res_df.index)
        
        # Apply the fix from reporting.py
        if hasattr(model, '__class__') and 'SAITS' in model.__class__.__name__:
            pred_indices = res_df.index[pred_mask]
            min_len = min(len(pred_indices), len(predictions))
            if min_len > 0:
                full_pred.loc[pred_indices[:min_len]] = predictions[:min_len]
                print(f"   ✅ Successfully handled length mismatch: used {min_len} predictions")
            else:
                print("   ⚠️  No predictions to assign")
        else:
            full_pred.loc[pred_mask] = predictions
        
        # Check that we have some valid predictions
        valid_predictions = ~np.isnan(full_pred)
        print(f"   📊 Valid predictions: {valid_predictions.sum()}/{len(full_pred)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Prediction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 Testing SAITS Fixes")
    print("=" * 50)
    
    # Test 1: Performance optimization
    perf_ok = test_performance_optimization()
    
    # Test 2: Prediction length handling
    pred_ok = test_prediction_length_handling()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   Performance Optimization: {'✅ PASS' if perf_ok else '❌ FAIL'}")
    print(f"   Prediction Length Handling: {'✅ PASS' if pred_ok else '❌ FAIL'}")
    
    if perf_ok and pred_ok:
        print("\n🎉 All tests passed! SAITS fixes are working correctly.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
