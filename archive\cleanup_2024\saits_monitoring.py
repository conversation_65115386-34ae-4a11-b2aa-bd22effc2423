"""
SAITS Performance Monitoring Module
===================================

This module provides comprehensive monitoring and logging for SAITS model training
and preprocessing operations.

Key Features:
1. Real-time performance tracking
2. Memory usage monitoring
3. Training progress logging
4. Error detection and reporting
5. Performance metrics visualization
"""

import time
import logging
import psutil
import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('saits_training.log')
    ]
)
logger = logging.getLogger(__name__)


class SAITSMonitor:
    """
    Comprehensive monitoring system for SAITS training and preprocessing.
    """

    def __init__(self, log_file: str = "saits_performance.log",
                 save_plots: bool = True,
                 plot_interval: int = 10):
        """
        Initialize SAITS monitor.

        Args:
            log_file: Path to save performance logs
            save_plots: Whether to save performance plots
            plot_interval: Interval for updating plots (epochs)
        """
        self.log_file = log_file
        self.save_plots = save_plots
        self.plot_interval = plot_interval

        # Performance tracking
        self.metrics = {
            'preprocessing': {
                'start_time': None,
                'end_time': None,
                'duration': None,
                'data_shape': None,
                'nan_ratio': None,
                'memory_usage': []
            },
            'training': {
                'start_time': None,
                'end_time': None,
                'total_duration': None,
                'epochs': [],
                'losses': [],
                'learning_rates': [],
                'batch_times': [],
                'memory_usage': [],
                'gpu_utilization': []
            },
            'validation': {
                'losses': [],
                'metrics': []
            },
            'system': {
                'cpu_usage': [],
                'ram_usage': [],
                'gpu_memory': [],
                'timestamps': []
            }
        }

        # Error tracking
        self.errors = []
        self.warnings = []

        logger.info(f"SAITSMonitor initialized - logging to {log_file}")

    def start_preprocessing(self, data_shape: tuple, nan_ratio: float):
        """
        Start monitoring preprocessing phase.

        Args:
            data_shape: Shape of input data
            nan_ratio: Ratio of NaN values in data
        """
        self.metrics['preprocessing']['start_time'] = time.time()
        self.metrics['preprocessing']['data_shape'] = data_shape
        self.metrics['preprocessing']['nan_ratio'] = nan_ratio

        logger.info(f"🔄 Preprocessing started - Data shape: {data_shape}, NaN ratio: {nan_ratio:.2%}")
        self._log_system_stats()

    def end_preprocessing(self):
        """End monitoring preprocessing phase."""
        end_time = time.time()
        self.metrics['preprocessing']['end_time'] = end_time
        self.metrics['preprocessing']['duration'] = end_time - self.metrics['preprocessing']['start_time']

        duration = self.metrics['preprocessing']['duration']
        logger.info(f"✅ Preprocessing completed in {duration:.2f} seconds")
        self._log_system_stats()

    def start_training(self):
        """Start monitoring training phase."""
        self.metrics['training']['start_time'] = time.time()
        logger.info("🚀 Training started")
        self._log_system_stats()

    def log_epoch(self, epoch: int, loss: float, learning_rate: float,
                  batch_time: float, validation_loss: Optional[float] = None):
        """
        Log metrics for a training epoch.

        Args:
            epoch: Current epoch number
            loss: Training loss
            learning_rate: Current learning rate
            batch_time: Average batch processing time
            validation_loss: Validation loss (if available)
        """
        self.metrics['training']['epochs'].append(epoch)
        self.metrics['training']['losses'].append(loss)
        self.metrics['training']['learning_rates'].append(learning_rate)
        self.metrics['training']['batch_times'].append(batch_time)

        if validation_loss is not None:
            self.metrics['validation']['losses'].append(validation_loss)

        # Log system stats
        self._log_system_stats()

        # Log progress
        if epoch % self.plot_interval == 0:
            logger.info(f"Epoch {epoch}: Loss={loss:.4f}, LR={learning_rate:.6f}, "
                       f"Batch_time={batch_time:.3f}s")

            if self.save_plots:
                self._update_plots()

    def log_error(self, error: Exception, context: str):
        """
        Log an error with context.

        Args:
            error: Exception that occurred
            context: Context where error occurred
        """
        error_info = {
            'timestamp': datetime.now().isoformat(),
            'context': context,
            'error_type': type(error).__name__,
            'error_message': str(error)
        }

        self.errors.append(error_info)
        logger.error(f"❌ Error in {context}: {error}")

    def log_warning(self, message: str, context: str):
        """
        Log a warning with context.

        Args:
            message: Warning message
            context: Context where warning occurred
        """
        warning_info = {
            'timestamp': datetime.now().isoformat(),
            'context': context,
            'message': message
        }

        self.warnings.append(warning_info)
        logger.warning(f"⚠️ Warning in {context}: {message}")

    def _log_system_stats(self):
        """Log current system statistics."""
        timestamp = time.time()

        # CPU and RAM usage
        cpu_percent = psutil.cpu_percent()
        ram_percent = psutil.virtual_memory().percent

        self.metrics['system']['timestamps'].append(timestamp)
        self.metrics['system']['cpu_usage'].append(cpu_percent)
        self.metrics['system']['ram_usage'].append(ram_percent)

        # GPU memory if available
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.memory_allocated() / 1024**3  # GB
            self.metrics['system']['gpu_memory'].append(gpu_memory)
            self.metrics['training']['memory_usage'].append(gpu_memory)
        else:
            self.metrics['system']['gpu_memory'].append(0)
            self.metrics['training']['memory_usage'].append(0)