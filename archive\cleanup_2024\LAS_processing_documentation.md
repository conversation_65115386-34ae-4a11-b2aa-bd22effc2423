# Well‑Log Imputation Toolkit — *LAS Processing & I/O* Module  
*(Model‑agnostic documentation)*

This document explains every function that **reads**, **cleans**, and **writes** LAS well‑log data inside the codebase you just downloaded.  
It is deliberately **model‑agnostic**: the machine‑learning layer (`ml_core.py`) is out of scope, so you can integrate **any** predictive approach on top of these utilities.

---

## 1. Directory & File Map

```
log_imputation_project/
├── data_handler.py      # **★ LAS I/O & utilities**
├── config_handler.py    # CLI helpers (paths, log selection, well split)
├── reporting.py         # Quick‑look QC (plots/text)
├── ml_core.py           # ML registry & impute_logs  ← *not covered here*
├── main.py              # Orchestrator script
```

The **heart** of LAS processing lives in **`data_handler.py`**.  
`config_handler.py` and `reporting.py` provide small, LAS‑aware helpers.

---

## 2. `data_handler.py` in Depth

### 2.1 `load_las_files_from_directory(input_dir) → (DataFrame, dict, list, list)`

| Returns | Type | Meaning |
|---------|------|---------|
| **`combined`** | `pd.DataFrame` | All wells stacked vertically (long format)<br>Columns: `MD`, `WELL`, & every curve present in any LAS |
| **`las_objects`** | `dict[str, lasio.LASFile]` | Raw `lasio` objects keyed by well name |
| **`wells`** | `list[str]` | Sorted unique well names |
| **`log_names`** | `list[str]` | Alphabetical list of curve mnemonics (excluding depth + WELL) |

**Workflow**

1. **Enumerate** `*.las` files in the folder.  
2. **Read** each file with `lasio.read()` (handles v1.2+).  
3. **Determine depth column** (first match in `['DEPT', 'DEPTH', 'MD']`).  
4. **Rename** depth column → `MD` to standardise.  
5. **Add** a `WELL` column using `WELL` header (fallback: filename stem).  
6. **Concatenate** all frames (`pd.concat`).  
7. **Collect** curve names **excluding** `MD` & `WELL`.

> ⚠️  If any LAS lacks a depth mnemonic, the function skips it and prints a warning.

### 2.2 `clean_log_data(df) → DataFrame`

A **very lightweight** sanity‑check:

```python
rules = {
    'GR'  : (0, 300),   # API
    'NPHI': (0, 1),     # Fraction
    'RHOB': (1.5, 3.0), # g/cc
    'DT'  : (40, 200)   # µs/ft (compressional)
}
```

* For every column present in `rules`, values outside the min–max window are replaced by **`NaN`** (so they become candidates for imputation later).
* No interpolation, smoothing or resampling is performed — the principle is **“let the ML handle gaps”**.

> ➕ **Extending**: just expand the `rules` dict or swap in a more advanced validator (e.g., z‑score clipping).

### 2.3 `write_results_to_las(res_df, target_log, las_objects, output_dir)`

Creates **new LAS files** containing:

| New Curve | Description | Source |
|-----------|-------------|--------|
| `{target}_imputed` | Original values where present **+** ML‑filled gaps | `res_df` |
| `{target}_pred`    | Complete model prediction (all rows imputed or not) | `res_df` |
| `{target}_error`   | \|original − prediction\| for QC (only at sampled depths) | `res_df` |

**Steps**

1. For each **well** in `las_objects`  
   a. Slice `res_df` for that well.  
   b. Copy the original `lasio.LASFile` (preserves headers).  
   c. **Delete** existing curves with the same names to avoid duplicates.  
   d. **Append** new curves using `las.append_curve(<mnemonic>, data, unit, descr)`.  
   e. Save as `{well}_imputed.las` in `output_dir` (LAS v2.0).

All original metadata (well, curve, parameter sections) are retained; only curves are augmented.

---

## 3. `config_handler.py` — Path & Selection Helpers

Although not doing heavy lifting, these functions orchestrate *which* logs and wells feed into the LAS pipeline:

### • `get_io_paths()`  
Simple CLI prompts for **input** (folder with LAS) & **output** directories.

### • `configure_log_selection(logs)`  
Interactive choice of:
1. **Feature logs** (independent variables) — multiple selection.  
2. **Target log** (to be imputed) — single selection.

### • `configure_well_separation(wells)`  
Allows either:
- **`mixed`** mode (train + predict on all wells)  
- **`separated`** mode (choose distinct training vs. prediction wells)

The returned dict travels unchanged through the pipeline, and `data_handler.py` relies on its `mode`/`wells` fields to subset the **combined** dataframe.

### • `get_prediction_mode()`  
Flags the *imputation style* (fill gaps only, cross‑validation, or overwrite).

---

## 4. `reporting.py` — Quick‑Look QC (LAS‑aware)

| Function | Purpose | LAS Tie‑in |
|----------|---------|-----------|
| `generate_qc_report` | Prints curve coverage (% non‑NaN) **before** ML | Uses same dataframe produced by `load_las_files_from_directory` |
| `create_summary_plots` | Depth‑aligned overlay of **original vs. imputed** curves per well | Reads `{target}`, `{target}_imputed` columns from DF |
| `generate_final_report` | Text summary of error metrics (MAE, R²) | Relies on ML layer for numbers, but model‑agnostic |

---

## 5. `main.py` — End‑to‑End Flow (LAS perspective)

```mermaid
graph TD
  A[Prompt for paths] --> B(load_las_files_from_directory)
  B --> C(clean_log_data)
  C --> D{Feature & target selection}
  D --> E{Well split}
  E --> F(generate_qc_report)
  F --> G(<< ML black‑box >>)  %% model step
  G --> H(create_summary_plots)
  G --> I(write_results_to_las)
```

Even if you swap in your own regression or statistical filler inside **G**, nothing else needs to change — 
as long as you return a dataframe with:

* original columns (`MD`, `WELL`, logs…)  
* `{target}_imputed`, `{target}_pred`, `{target}_error`

---

## 6. Dependency Snapshot

```txt
lasio >= 0.33
pandas >= 2.0
numpy  >= 1.24
matplotlib >= 3.7
scikit‑learn >= 1.4   # only for QC plots (metrics)
```

No LAS version or wireline‑company specific libraries are used — pure **`lasio`** for maximum portability.

---

## 7. Customising the LAS Pipeline

| Need | Where to tweak |
|------|----------------|
| Different depth mnemonic | `load_las_files_from_directory` → adjust depth detection list |
| Stricter or curve‑specific cleaning | `clean_log_data` rules dict |
| Additional QC curves (e.g., σ) | `write_results_to_las` append curve loop |
| Export LAS v1.2 | last line of `write_results_to_las` (`version=1.2`) |

---

## 8. Minimal *No‑ML* Example

```python
from data_handler import load_las_files_from_directory, clean_log_data, write_results_to_las

df, las_objs, wells, logs = load_las_files_from_directory("./las_in")
df_clean = clean_log_data(df)

# --- Your custom filler here ---
df_clean["RHOB_imputed"] = df_clean["RHOB"].fillna(method="ffill")  # trivial example
df_clean["RHOB_pred"]    = df_clean["RHOB_imputed"]
df_clean["RHOB_error"]   = abs(df_clean["RHOB"] - df_clean["RHOB_pred"])

write_results_to_las(df_clean, "RHOB", las_objs, "./las_out")
```

Run it, and the script will create new `*_imputed.las` files, **independent** of any ML code.

---

### Happy logging!
