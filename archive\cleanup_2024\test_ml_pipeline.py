#!/usr/bin/env python3
"""
Test script to verify the ML pipeline works with the SimpleSAITSRegressor fix.
"""

import numpy as np
import pandas as pd
import sys
import traceback

def test_ml_pipeline_integration():
    """Test that the ML pipeline can instantiate models without TypeError."""
    print("🧪 Testing ML pipeline integration...")
    
    try:
        from ml_core import MODEL_REGISTRY
        from config_handler import configure_hyperparameters
        from main import optimize_saits_hyperparameters
        
        # Get hyperparameters
        print("   Getting hyperparameters...")
        hparams = configure_hyperparameters()
        
        # Optimize for GPU (this was causing the TypeError)
        print("   Optimizing hyperparameters for GPU...")
        optimized_hparams = optimize_saits_hyperparameters(hparams, gpu_enabled=True)
        
        # Try to instantiate all models (this is where the error occurred)
        print("   Instantiating models from registry...")
        models = {}
        for k in MODEL_REGISTRY:
            try:
                model_config = MODEL_REGISTRY[k]
                model_class = model_config['model_class']
                model_hparams = optimized_hparams[k]
                
                print(f"     Creating {model_config['name']} model...")
                model = model_class(**model_hparams)
                models[model_config['name']] = model
                print(f"     ✅ {model_config['name']} instantiated successfully")
                
            except Exception as e:
                print(f"     ❌ Failed to instantiate {model_config['name']}: {e}")
                raise e
        
        print(f"   ✅ Successfully instantiated {len(models)} models")
        return True
        
    except Exception as e:
        print(f"   ❌ ML pipeline test failed: {e}")
        traceback.print_exc()
        return False

def test_simple_training():
    """Test a simple training scenario with SimpleSAITSRegressor."""
    print("\n🧪 Testing simple training scenario...")
    
    try:
        from simple_saits import SimpleSAITSRegressor
        
        # Create synthetic data
        np.random.seed(42)
        n_samples = 100
        n_features = 3
        
        X = np.random.randn(n_samples, n_features)
        y = np.random.randn(n_samples)
        
        # Add some NaN values to simulate missing data
        X[np.random.choice(n_samples, 5), np.random.choice(n_features, 1)] = np.nan
        y[np.random.choice(n_samples, 3)] = np.nan
        
        print(f"   Created test data: X{X.shape}, y{y.shape}")
        print(f"   NaN values in X: {np.sum(np.isnan(X))}")
        print(f"   NaN values in y: {np.sum(np.isnan(y))}")
        
        # Test with d_inner parameter (this was causing the TypeError)
        model = SimpleSAITSRegressor(
            sequence_length=20,
            d_model=32,
            n_head=2,
            epochs=2,  # Very short for testing
            batch_size=16,
            d_inner=64,  # This parameter was causing the issue
            patience=1
        )
        
        print("   Training model...")
        model.fit(X, y, verbose=True)
        
        print("   Making predictions...")
        predictions = model.predict(X[:10])
        
        print(f"   ✅ Predictions shape: {predictions.shape}")
        print(f"   ✅ Valid predictions: {np.sum(~np.isnan(predictions))}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Simple training test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Testing ML Pipeline with SimpleSAITSRegressor Fix")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test 1: ML pipeline integration
    if not test_ml_pipeline_integration():
        all_tests_passed = False
    
    # Test 2: Simple training
    if not test_simple_training():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 All ML pipeline tests passed!")
        print("✅ The TypeError in SimpleSAITSRegressor has been successfully fixed.")
        print("✅ The ML log prediction system is now working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
