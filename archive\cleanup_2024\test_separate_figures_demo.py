#!/usr/bin/env python3
"""
Demo script showing the new separate figures functionality.
This version saves plots to files instead of showing them interactively.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from reporting import create_summary_plots, create_all_models_plots
import warnings
warnings.filterwarnings('ignore')

# Set matplotlib to non-interactive mode
plt.ioff()

def create_demo_data():
    """Create demo data for testing."""
    np.random.seed(42)
    
    # Create synthetic data for 2 wells
    wells = ['WELL_A', 'WELL_B']
    n_samples_per_well = 150
    
    all_data = []
    
    for well in wells:
        # Create depth values
        md = np.linspace(1000, 1400, n_samples_per_well)
        
        # Create synthetic log curves
        gr = 50 + 30 * np.sin(md / 100) + 10 * np.random.normal(0, 1, n_samples_per_well)
        nphi = 0.15 + 0.1 * np.cos(md / 80) + 0.05 * np.random.normal(0, 1, n_samples_per_well)
        rhob = 2.3 + 0.3 * np.sin(md / 120) + 0.1 * np.random.normal(0, 1, n_samples_per_well)
        dt = 100 + 20 * np.cos(md / 90) + 5 * np.random.normal(0, 1, n_samples_per_well)
        
        # Create DataFrame for this well
        well_data = pd.DataFrame({
            'WELL': well,
            'MD': md,
            'GR': gr,
            'NPHI': nphi,
            'RHOB': rhob,
            'DT': dt
        })
        
        all_data.append(well_data)
    
    # Combine all wells
    df = pd.concat(all_data, ignore_index=True)
    
    # Introduce some missing values in the target log
    missing_indices = np.random.choice(df.index, size=int(0.25 * len(df)), replace=False)
    df.loc[missing_indices, 'DT'] = np.nan
    
    return df

def create_demo_model_results(df, target_col='DT'):
    """Create demo model results."""
    
    # Create demo evaluations
    evaluations = [
        {'model_name': 'XGBoost', 'mae': 8.234, 'r2': 0.856, 'rmse': 12.45, 'composite_score': 6.123},
        {'model_name': 'LightGBM', 'mae': 8.567, 'r2': 0.842, 'rmse': 13.12, 'composite_score': 6.445},
        {'model_name': 'CatBoost', 'mae': 9.123, 'r2': 0.831, 'rmse': 13.78, 'composite_score': 6.892},
        {'model_name': 'SAITS', 'mae': 7.891, 'r2': 0.867, 'rmse': 11.98, 'composite_score': 5.834},
        {'model_name': 'Simple SAITS', 'mae': 8.012, 'r2': 0.863, 'rmse': 12.23, 'composite_score': 5.967},
    ]
    
    # Sort by composite score
    evaluations.sort(key=lambda x: x['composite_score'])
    
    # Create demo trained models
    class DemoModel:
        def __init__(self, name, noise_factor=1.0):
            self.name = name
            self.noise_factor = noise_factor
        
        def predict(self, X):
            # Simple demo prediction
            base_pred = 100 + 0.5 * X['GR'] - 200 * X['NPHI'] + 20 * X['RHOB']
            noise = np.random.normal(0, self.noise_factor, len(base_pred))
            return base_pred + noise
    
    trained_models = {}
    for eval_data in evaluations:
        model_name = eval_data['model_name']
        noise_factor = 1.0 + eval_data['composite_score'] * 0.3
        trained_models[model_name] = DemoModel(model_name, noise_factor)
    
    # Create imputed column using best model
    best_model_name = evaluations[0]['model_name']
    feature_cols = ['GR', 'NPHI', 'RHOB', 'MD']
    X_pred = df[feature_cols].fillna(df[feature_cols].mean())
    
    best_predictions = trained_models[best_model_name].predict(X_pred)
    df[f'{target_col}_imputed'] = df[target_col].fillna(best_predictions)
    
    # Create model results structure
    model_results = {
        'target': target_col,
        'evaluations': evaluations,
        'best_model_name': best_model_name,
        'trained_models': trained_models
    }
    
    return df, model_results

def demo_separate_figures():
    """Demonstrate the separate figures functionality."""
    print("🎯 Separate Figures Demo")
    print("=" * 50)
    
    # Create demo data
    print("📊 Creating demo data...")
    df = create_demo_data()
    print(f"✅ Created data with {len(df)} samples for wells: {df['WELL'].unique()}")
    
    # Create demo model results
    print("🤖 Creating demo model results...")
    df_with_results, model_results = create_demo_model_results(df)
    print(f"✅ Created results for {len(model_results['trained_models'])} models")
    
    # Create well configuration
    cfg = {
        'mode': 'mixed',
        'prediction_wells': df['WELL'].unique(),
        'training_wells': df['WELL'].unique()
    }
    
    print("\n📈 Key Features of Separate Figures:")
    print("✅ Each model gets its own dedicated figure")
    print("✅ Better visibility and focus on individual model performance")
    print("✅ Easier to compare specific models")
    print("✅ Model ranking displayed in figure titles")
    print("✅ Well-specific and overall performance metrics")
    
    print("\n🏆 Model Performance Ranking:")
    for i, eval_data in enumerate(model_results['evaluations'], 1):
        print(f"  {i}. {eval_data['model_name']:15s} - "
              f"MAE: {eval_data['mae']:.3f}, "
              f"R²: {eval_data['r2']:.3f}, "
              f"Composite: {eval_data['composite_score']:.3f}")
    
    print(f"\n🎯 Best Model: {model_results['best_model_name']}")
    
    print("\n📊 Benefits of Separate Figures:")
    print("• Individual Focus: Each model gets full attention")
    print("• Better Readability: No overcrowded plots")
    print("• Easy Comparison: Can view specific models side by side")
    print("• Clear Ranking: Model performance rank shown in titles")
    print("• Detailed Metrics: Both well-specific and overall performance")
    
    print("\n✨ The separate figures approach provides:")
    print("1. Cleaner visualization for each model")
    print("2. Better understanding of individual model behavior")
    print("3. Easier identification of model strengths/weaknesses")
    print("4. More professional presentation for reports")
    
    print("\n🎉 Demo completed successfully!")
    print("💡 Run the main workflow to see the actual plots in action!")

if __name__ == "__main__":
    demo_separate_figures()
