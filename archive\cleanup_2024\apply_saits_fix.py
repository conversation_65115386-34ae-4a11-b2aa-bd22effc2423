#!/usr/bin/env python3
"""
Apply SAITS Fix Script
=====================

This script applies the comprehensive SAITS NaN fix to your existing implementation.
It replaces the problematic SAITS models with stable versions.
"""

import os
import shutil
from pathlib import Path

def backup_original_files():
    """Create backups of original files."""
    print("📁 Creating backups of original files...")
    
    files_to_backup = [
        "saits_model.py",
        "simple_saits.py"
    ]
    
    backup_dir = Path("backup_original")
    backup_dir.mkdir(exist_ok=True)
    
    for file_name in files_to_backup:
        if Path(file_name).exists():
            backup_path = backup_dir / file_name
            shutil.copy2(file_name, backup_path)
            print(f"   ✅ Backed up {file_name} to {backup_path}")
        else:
            print(f"   ⚠️  File {file_name} not found, skipping backup")
    
    print("✅ Backup completed")

def apply_saits_model_fix():
    """Apply the fix to saits_model.py."""
    print("🔧 Applying fix to saits_model.py...")
    
    # Read the fixed implementation
    try:
        with open("saits_model_fixed.py", "r", encoding="utf-8") as f:
            fixed_content = f.read()
        
        # Replace the SAITSRegressor class with StableSAITSRegressor
        fixed_content = fixed_content.replace("class StableSAITSRegressor", "class SAITSRegressor")
        fixed_content = fixed_content.replace("StableSAITS(", "SAITS(")
        fixed_content = fixed_content.replace("class StableSAITS", "class SAITS")
        
        # Write to the original file
        with open("saits_model.py", "w", encoding="utf-8") as f:
            f.write(fixed_content)
        
        print("   ✅ saits_model.py updated with stable implementation")
        
    except Exception as e:
        print(f"   ❌ Failed to update saits_model.py: {e}")
        return False
    
    return True

def apply_simple_saits_fix():
    """Apply the fix to simple_saits.py."""
    print("🔧 Applying fix to simple_saits.py...")
    
    # For Simple SAITS, we'll create a wrapper that uses the stable components
    simple_saits_content = '''#!/usr/bin/env python3
"""
Simple SAITS Implementation - Fixed Version
==========================================

This is a simplified SAITS implementation with comprehensive NaN handling.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler
from sklearn.base import BaseEstimator, RegressorMixin

# Import stable components from the fixed SAITS model
from saits_model import (
    debug_tensor, safe_tensor_operation, StablePositionalEncoding,
    StableMultiHeadAttention, StablePositionwiseFeedForward,
    validate_model_inputs, stable_masked_mae_cal
)

class SimpleSAITSRegressor(BaseEstimator, RegressorMixin):
    """
    Simplified SAITS implementation with numerical stability.
    
    This version uses fewer parameters but maintains the same stability improvements.
    """
    
    def __init__(self, sequence_length=50, d_model=64, n_head=4, n_layers=2,
                 dropout=0.1, epochs=100, batch_size=32, learning_rate=0.001,
                 patience=10, random_state=42, device=None, d_inner=None, **kwargs):

        # Note: d_inner parameter is accepted for compatibility with GPU optimizer
        # but is not used in SimpleSAITSRegressor (it's calculated as d_model * 2)

        # Store parameters with validation
        self.sequence_length = max(sequence_length, 10)
        self.d_model = max(d_model, 4)
        self.n_head = max(n_head, 1)
        self.n_layers = max(n_layers, 1)
        self.dropout = max(min(dropout, 0.9), 0.0)
        self.epochs = max(epochs, 1)
        self.batch_size = max(batch_size, 1)
        self.learning_rate = max(learning_rate, 1e-6)
        self.patience = max(patience, 1)
        
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        print(f"🔧 SimpleSAITSRegressor initialized with device: {self.device}")
        
        # Initialize components
        self.model = None
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        self.feature_names_ = None
        
        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)
    
    def fit(self, X, y, eval_set=None, verbose=False):
        """Fit the Simple SAITS model using stable implementation."""
        print("🚀 Starting SimpleSAITS training...")
        
        # Use the same stable training approach as the full SAITS
        # This is a simplified wrapper that delegates to the stable implementation
        from saits_model import SAITSRegressor
        
        # Create a SAITS model with simplified parameters
        self.model = SAITSRegressor(
            sequence_length=self.sequence_length,
            n_groups=1,  # Simplified: single group
            n_group_inner_layers=self.n_layers,
            d_model=self.d_model,
            d_inner=self.d_model * 2,  # Simplified: 2x d_model
            n_head=self.n_head,
            d_k=self.d_model // self.n_head,
            d_v=self.d_model // self.n_head,
            dropout=self.dropout,
            epochs=self.epochs,
            batch_size=self.batch_size,
            learning_rate=self.learning_rate,
            patience=self.patience,
            device=self.device
        )
        
        # Delegate to the stable implementation
        self.model.fit(X, y, eval_set=eval_set, verbose=verbose)
        
        # Copy fitted attributes
        self.feature_names_ = self.model.feature_names_
        self.scaler_X = self.model.scaler_X
        self.scaler_y = self.model.scaler_y
        
        print("✅ SimpleSAITS training completed")
        return self
    
    def predict(self, X):
        """Predict using the trained Simple SAITS model."""
        if self.model is None:
            raise ValueError("Model must be fitted before prediction")
        
        return self.model.predict(X)

# Test function
def test_simple_saits():
    """Test the Simple SAITS implementation."""
    print("🧪 Testing SimpleSAITS implementation...")
    
    # Create test data
    np.random.seed(42)
    n_samples = 100
    n_features = 3
    
    X = np.random.randn(n_samples, n_features)
    y = np.random.randn(n_samples)
    
    # Add some NaN values
    X[np.random.choice(n_samples, 10), np.random.choice(n_features, 1)] = np.nan
    y[np.random.choice(n_samples, 5)] = np.nan
    
    print(f"   📊 Test data: X{X.shape}, y{y.shape}")
    
    try:
        model = SimpleSAITSRegressor(
            sequence_length=15,
            epochs=3,
            batch_size=8,
            verbose=True
        )
        
        model.fit(X, y, verbose=True)
        predictions = model.predict(X)
        
        print(f"   ✅ Predictions shape: {predictions.shape}")
        print("✅ SimpleSAITS test passed!")
        
    except Exception as e:
        print(f"❌ SimpleSAITS test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Simple SAITS - Testing")
    print("=" * 50)
    test_simple_saits()
'''
    
    try:
        with open("simple_saits.py", "w", encoding="utf-8") as f:
            f.write(simple_saits_content)
        
        print("   ✅ simple_saits.py updated with stable implementation")
        
    except Exception as e:
        print(f"   ❌ Failed to update simple_saits.py: {e}")
        return False
    
    return True

def verify_fix():
    """Verify that the fix has been applied correctly."""
    print("🔍 Verifying fix application...")
    
    try:
        # Test import of fixed modules
        import saits_model
        import simple_saits
        
        print("   ✅ Modules import successfully")
        
        # Test basic instantiation
        saits_model.SAITSRegressor(epochs=1)
        simple_saits.SimpleSAITSRegressor(epochs=1)
        
        print("   ✅ Models instantiate successfully")
        print("✅ Fix verification completed")
        return True
        
    except Exception as e:
        print(f"   ❌ Fix verification failed: {e}")
        return False

def main():
    """Main function to apply the SAITS fix."""
    print("🚀 SAITS NaN Fix Application")
    print("=" * 50)
    
    print("This script will apply comprehensive fixes to resolve the 'Input contains NaN' error.")
    print("The fixes include:")
    print("  • Numerical stability improvements")
    print("  • Comprehensive NaN handling")
    print("  • Robust error handling")
    print("  • Input validation")
    print()
    
    # Ask for confirmation
    response = input("Do you want to proceed? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ Fix application cancelled")
        return
    
    # Apply fixes
    try:
        backup_original_files()
        
        if apply_saits_model_fix():
            print("✅ SAITS model fix applied")
        else:
            print("❌ SAITS model fix failed")
            return
        
        if apply_simple_saits_fix():
            print("✅ Simple SAITS fix applied")
        else:
            print("❌ Simple SAITS fix failed")
            return
        
        if verify_fix():
            print("\n🎉 SAITS fix application completed successfully!")
            print("\n📋 Next steps:")
            print("1. Run your ML pipeline again")
            print("2. The 'Input contains NaN' error should be resolved")
            print("3. SAITS models should now work with your data")
            print("4. GPU optimizations should work correctly")
        else:
            print("\n❌ Fix verification failed")
            
    except Exception as e:
        print(f"\n❌ Fix application failed: {e}")

if __name__ == "__main__":
    main()
