"""
Enhanced SAITS Preprocessing Module
==================================

This module provides comprehensive data preprocessing for SAITS models to resolve
"Input contains NaN" errors and optimize performance.

Key Features:
1. NaN handling with proper masking
2. Data normalization considering missing values
3. GPU optimization support
4. Comprehensive validation
5. Performance monitoring
"""

import numpy as np
import pandas as pd
import torch
import logging
from sklearn.preprocessing import StandardScaler, RobustScaler
from typing import Dict, Tuple, Optional, Union, Any
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SAITSPreprocessor:
    """
    Comprehensive SAITS data preprocessor that handles NaN values,
    normalization, and creates proper input format for SAITS models.
    """

    def __init__(self,
                 normalization_method: str = 'standard',
                 handle_outliers: bool = True,
                 outlier_threshold: float = 3.0,
                 min_valid_ratio: float = 0.1,
                 device: str = 'auto'):
        """
        Initialize SAITS preprocessor.

        Args:
            normalization_method: 'standard', 'robust', or 'minmax'
            handle_outliers: Whether to handle outliers before normalization
            outlier_threshold: Z-score threshold for outlier detection
            min_valid_ratio: Minimum ratio of valid values required per feature
            device: 'auto', 'cpu', or 'cuda'
        """
        self.normalization_method = normalization_method
        self.handle_outliers = handle_outliers
        self.outlier_threshold = outlier_threshold
        self.min_valid_ratio = min_valid_ratio

        # Set device
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)

        # Initialize scalers
        if normalization_method == 'standard':
            self.scaler = StandardScaler()
        elif normalization_method == 'robust':
            self.scaler = RobustScaler()
        else:
            raise ValueError(f"Unsupported normalization method: {normalization_method}")

        # Store preprocessing statistics
        self.preprocessing_stats = {}
        self.is_fitted = False

        logger.info(f"SAITSPreprocessor initialized with device: {self.device}")

    def _validate_input_data(self, data: np.ndarray) -> Dict[str, Any]:
        """
        Validate input data and return statistics.

        Args:
            data: Input data array

        Returns:
            Dictionary with validation statistics
        """
        stats = {
            'shape': data.shape,
            'total_values': data.size,
            'nan_count': np.sum(np.isnan(data)),
            'inf_count': np.sum(np.isinf(data)),
            'finite_count': np.sum(np.isfinite(data))
        }

        stats['nan_ratio'] = stats['nan_count'] / stats['total_values']
        stats['valid_ratio'] = stats['finite_count'] / stats['total_values']

        # Per-feature statistics
        stats['feature_stats'] = []
        for i in range(data.shape[1]):
            feature_data = data[:, i]
            feature_stats = {
                'feature_idx': i,
                'nan_count': np.sum(np.isnan(feature_data)),
                'nan_ratio': np.sum(np.isnan(feature_data)) / len(feature_data),
                'valid_count': np.sum(np.isfinite(feature_data)),
                'valid_ratio': np.sum(np.isfinite(feature_data)) / len(feature_data)
            }

            if feature_stats['valid_ratio'] < self.min_valid_ratio:
                logger.warning(f"Feature {i} has only {feature_stats['valid_ratio']:.2%} valid values")

            stats['feature_stats'].append(feature_stats)

        logger.info(f"Data validation: {stats['valid_ratio']:.2%} valid values, "
                   f"{stats['nan_ratio']:.2%} NaN values")

        return stats

    def _handle_outliers(self, data: np.ndarray, missing_mask: np.ndarray) -> np.ndarray:
        """
        Handle outliers using z-score method, considering missing values.

        Args:
            data: Input data
            missing_mask: Boolean mask (True for valid values)

        Returns:
            Data with outliers handled
        """
        if not self.handle_outliers:
            return data

        data_cleaned = data.copy()
        outlier_count = 0

        for i in range(data.shape[1]):
            feature_data = data[:, i]
            feature_mask = missing_mask[:, i]

            if np.sum(feature_mask) < 3:  # Need at least 3 valid values
                continue

            valid_data = feature_data[feature_mask]
            mean_val = np.mean(valid_data)
            std_val = np.std(valid_data)

            if std_val == 0:  # Constant feature
                continue

            z_scores = np.abs((feature_data - mean_val) / std_val)
            outlier_mask = (z_scores > self.outlier_threshold) & feature_mask

            # Replace outliers with median of valid values
            if np.any(outlier_mask):
                median_val = np.median(valid_data)
                data_cleaned[outlier_mask, i] = median_val
                outlier_count += np.sum(outlier_mask)

        if outlier_count > 0:
            logger.info(f"Handled {outlier_count} outliers")

        return data_cleaned

    def _create_missing_mask(self, data: np.ndarray) -> np.ndarray:
        """
        Create missing value mask (True for observed, False for missing).

        Args:
            data: Input data array

        Returns:
            Boolean mask array
        """
        # Create mask: True for valid (non-NaN, finite) values
        mask = np.isfinite(data)

        logger.info(f"Created missing mask: {np.sum(mask)} valid values out of {mask.size}")

        return mask.astype(np.float32)

    def _normalize_data(self, data: np.ndarray, missing_mask: np.ndarray,
                       fit_scaler: bool = False) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        Normalize data considering missing values.

        Args:
            data: Input data
            missing_mask: Boolean mask for valid values
            fit_scaler: Whether to fit the scaler

        Returns:
            Tuple of (normalized_data, normalization_stats)
        """
        # Prepare data for normalization
        data_for_scaling = data.copy()

        # Fill missing values with feature means for scaling
        for i in range(data.shape[1]):
            feature_data = data[:, i]
            feature_mask = missing_mask[:, i]

            if np.sum(feature_mask) > 0:
                feature_mean = np.mean(feature_data[feature_mask.astype(bool)])
                data_for_scaling[~feature_mask.astype(bool), i] = feature_mean
            else:
                # If no valid values, use 0
                data_for_scaling[:, i] = 0.0

        # Apply normalization
        if fit_scaler:
            normalized_data = self.scaler.fit_transform(data_for_scaling)
            normalization_stats = {
                'method': self.normalization_method,
                'feature_means': getattr(self.scaler, 'center_', getattr(self.scaler, 'mean_', None)),
                'feature_scales': getattr(self.scaler, 'scale_', None)
            }
        else:
            normalized_data = self.scaler.transform(data_for_scaling)
            normalization_stats = {'method': self.normalization_method}

        # Ensure missing values remain as zeros in normalized data
        normalized_data = normalized_data * missing_mask

        logger.info(f"Data normalized using {self.normalization_method} method")

        return normalized_data, normalization_stats

    def fit_transform(self, data: Union[np.ndarray, pd.DataFrame]) -> Dict[str, Any]:
        """
        Fit preprocessor and transform data for SAITS input.

        Args:
            data: Input data (numpy array or pandas DataFrame)

        Returns:
            Dictionary with processed data ready for SAITS
        """
        # Convert to numpy array if needed
        if isinstance(data, pd.DataFrame):
            data_array = data.values
            feature_names = list(data.columns)
        else:
            data_array = np.array(data)
            feature_names = [f"feature_{i}" for i in range(data_array.shape[1])]

        logger.info(f"Processing data with shape: {data_array.shape}")

        # Step 1: Validate input data
        validation_stats = self._validate_input_data(data_array)

        # Step 2: Create missing mask
        missing_mask = self._create_missing_mask(data_array)

        # Step 3: Handle outliers
        data_cleaned = self._handle_outliers(data_array, missing_mask)

        # Step 4: Normalize data
        normalized_data, norm_stats = self._normalize_data(
            data_cleaned, missing_mask, fit_scaler=True
        )

        # Step 5: Replace any remaining NaN with zeros
        processed_data = np.nan_to_num(normalized_data, nan=0.0, posinf=0.0, neginf=0.0)

        # Step 6: Convert to tensors
        X_tensor = torch.FloatTensor(processed_data).to(self.device)
        missing_mask_tensor = torch.FloatTensor(missing_mask).to(self.device)
        indicating_mask_tensor = missing_mask_tensor.clone()

        # Store preprocessing statistics
        self.preprocessing_stats = {
            'validation': validation_stats,
            'normalization': norm_stats,
            'feature_names': feature_names,
            'original_shape': data_array.shape,
            'processed_shape': processed_data.shape
        }

        self.is_fitted = True

        # Prepare SAITS input format
        saits_input = {
            'X': X_tensor,
            'missing_mask': missing_mask_tensor,
            'indicating_mask': indicating_mask_tensor,
            'original_data': data_array,
            'processed_data': processed_data,
            'preprocessing_stats': self.preprocessing_stats
        }

        logger.info("✅ Data preprocessing completed successfully")

        return saits_input

    def transform(self, data: Union[np.ndarray, pd.DataFrame]) -> Dict[str, Any]:
        """
        Transform new data using fitted preprocessor.

        Args:
            data: Input data to transform

        Returns:
            Dictionary with processed data ready for SAITS
        """
        if not self.is_fitted:
            raise ValueError("Preprocessor must be fitted before transform")

        # Convert to numpy array if needed
        if isinstance(data, pd.DataFrame):
            data_array = data.values
        else:
            data_array = np.array(data)

        logger.info(f"Transforming data with shape: {data_array.shape}")

        # Apply same preprocessing steps (without fitting)
        missing_mask = self._create_missing_mask(data_array)
        data_cleaned = self._handle_outliers(data_array, missing_mask)
        normalized_data, _ = self._normalize_data(data_cleaned, missing_mask, fit_scaler=False)
        processed_data = np.nan_to_num(normalized_data, nan=0.0, posinf=0.0, neginf=0.0)

        # Convert to tensors
        X_tensor = torch.FloatTensor(processed_data).to(self.device)
        missing_mask_tensor = torch.FloatTensor(missing_mask).to(self.device)
        indicating_mask_tensor = missing_mask_tensor.clone()

        saits_input = {
            'X': X_tensor,
            'missing_mask': missing_mask_tensor,
            'indicating_mask': indicating_mask_tensor,
            'original_data': data_array,
            'processed_data': processed_data
        }

        logger.info("✅ Data transformation completed")

        return saits_input

    def validate_preprocessing(self, saits_input: Dict[str, Any]) -> bool:
        """
        Validate that preprocessing was successful.

        Args:
            saits_input: Processed SAITS input dictionary

        Returns:
            True if validation passes
        """
        try:
            # Check for NaN values
            X = saits_input['X']
            missing_mask = saits_input['missing_mask']

            assert not torch.isnan(X).any(), "Processed data contains NaN values"
            assert not torch.isnan(missing_mask).any(), "Missing mask contains NaN values"

            # Check mask consistency
            assert X.shape == missing_mask.shape, "Data and mask shape mismatch"

            # Check mask values are binary
            unique_mask_values = torch.unique(missing_mask)
            assert torch.all((unique_mask_values == 0) | (unique_mask_values == 1)), \
                "Mask should contain only 0 and 1 values"

            # Check tensor device consistency
            assert X.device == missing_mask.device, "Tensors on different devices"

            logger.info("✅ Preprocessing validation passed")
            return True

        except AssertionError as e:
            logger.error(f"❌ Preprocessing validation failed: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error during validation: {e}")
            return False

    def get_preprocessing_summary(self) -> Dict[str, Any]:
        """
        Get summary of preprocessing operations.

        Returns:
            Dictionary with preprocessing summary
        """
        if not self.is_fitted:
            return {"status": "not_fitted"}

        return {
            "status": "fitted",
            "device": str(self.device),
            "normalization_method": self.normalization_method,
            "handle_outliers": self.handle_outliers,
            "preprocessing_stats": self.preprocessing_stats
        }


def preprocess_for_saits(data: Union[np.ndarray, pd.DataFrame],
                        **kwargs) -> Dict[str, Any]:
    """
    Convenience function for quick SAITS preprocessing.

    Args:
        data: Input data
        **kwargs: Additional arguments for SAITSPreprocessor

    Returns:
        Processed data ready for SAITS
    """
    preprocessor = SAITSPreprocessor(**kwargs)
    return preprocessor.fit_transform(data)