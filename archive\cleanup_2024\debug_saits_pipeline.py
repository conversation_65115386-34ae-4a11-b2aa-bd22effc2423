#!/usr/bin/env python3
"""
SAITS Pipeline Debug Script
===========================

This script diagnoses why SAITS models are not appearing in ML pipeline outputs.
It tests each step of the pipeline to identify where SAITS execution might be failing.
"""

import numpy as np
import pandas as pd
import sys
import traceback
from sklearn.model_selection import train_test_split

def create_test_data():
    """Create realistic test data similar to log data."""
    np.random.seed(42)
    n_samples = 200
    
    # Create synthetic log data
    md = np.linspace(1000, 2000, n_samples)
    gr = 50 + 30 * np.sin(md / 100) + np.random.normal(0, 5, n_samples)
    dt = 2.3 + 0.2 * np.sin(md / 150) + np.random.normal(0, 0.1, n_samples)
    nphi = 0.15 + 0.1 * np.cos(md / 120) + np.random.normal(0, 0.02, n_samples)
    
    # Add some NaN values to simulate missing data
    nan_indices = np.random.choice(n_samples, size=int(0.1 * n_samples), replace=False)
    dt[nan_indices] = np.nan
    
    df = pd.DataFrame({
        'WELL': ['WELL_A'] * n_samples,
        'MD': md,
        'GR': gr,
        'DT': dt,
        'NPHI': nphi
    })
    
    return df

def test_model_imports():
    """Test if all models can be imported correctly."""
    print("🧪 Testing Model Imports")
    print("-" * 40)
    
    results = {}
    
    # Test traditional ML models
    try:
        from xgboost import XGBRegressor
        results['XGBoost'] = True
        print("✅ XGBoost imported successfully")
    except ImportError as e:
        results['XGBoost'] = False
        print(f"❌ XGBoost import failed: {e}")
    
    try:
        from lightgbm import LGBMRegressor
        results['LightGBM'] = True
        print("✅ LightGBM imported successfully")
    except ImportError as e:
        results['LightGBM'] = False
        print(f"❌ LightGBM import failed: {e}")
    
    try:
        from catboost import CatBoostRegressor
        results['CatBoost'] = True
        print("✅ CatBoost imported successfully")
    except ImportError as e:
        results['CatBoost'] = False
        print(f"❌ CatBoost import failed: {e}")
    
    # Test SAITS models
    try:
        from saits_model_fixed import SAITSRegressor
        results['SAITS'] = True
        print("✅ SAITS imported successfully")
    except ImportError as e:
        results['SAITS'] = False
        print(f"❌ SAITS import failed: {e}")
    
    try:
        from simple_saits import SimpleSAITSRegressor
        results['Simple SAITS'] = True
        print("✅ Simple SAITS imported successfully")
    except ImportError as e:
        results['Simple SAITS'] = False
        print(f"❌ Simple SAITS import failed: {e}")
    
    return results

def test_model_registry():
    """Test the MODEL_REGISTRY to see which models are available."""
    print("\n🧪 Testing Model Registry")
    print("-" * 40)
    
    try:
        from ml_core import MODEL_REGISTRY
        print(f"✅ MODEL_REGISTRY imported successfully")
        print(f"📋 Available models: {list(MODEL_REGISTRY.keys())}")
        
        # Check each model in registry
        for model_key, model_config in MODEL_REGISTRY.items():
            model_name = model_config['name']
            model_class = model_config['model_class']
            print(f"   • {model_key}: {model_name} ({model_class.__name__})")
        
        return True, MODEL_REGISTRY
    except ImportError as e:
        print(f"❌ MODEL_REGISTRY import failed: {e}")
        return False, None

def test_model_instantiation(model_registry):
    """Test if models can be instantiated with default hyperparameters."""
    print("\n🧪 Testing Model Instantiation")
    print("-" * 40)
    
    try:
        from config_handler import configure_hyperparameters
        hparams = configure_hyperparameters()
        
        instantiation_results = {}
        
        for model_key in model_registry.keys():
            try:
                model_config = model_registry[model_key]
                model_class = model_config['model_class']
                model_hparams = hparams[model_key]
                
                print(f"   Testing {model_config['name']}...")
                model = model_class(**model_hparams)
                instantiation_results[model_key] = True
                print(f"   ✅ {model_config['name']} instantiated successfully")
                
            except Exception as e:
                instantiation_results[model_key] = False
                print(f"   ❌ {model_config['name']} instantiation failed: {e}")
                print(f"      Hyperparameters: {model_hparams}")
        
        return instantiation_results
    except Exception as e:
        print(f"❌ Model instantiation test failed: {e}")
        return {}

def test_saits_training(df):
    """Test SAITS model training specifically."""
    print("\n🧪 Testing SAITS Model Training")
    print("-" * 40)
    
    # Prepare data
    feature_cols = ['GR', 'NPHI', 'MD']
    target_col = 'DT'
    
    # Clean data for training
    train_data = df[df[target_col].notna()].copy()
    X = train_data[feature_cols].fillna(train_data[feature_cols].mean())
    y = train_data[target_col]
    
    print(f"📊 Training data shape: X={X.shape}, y={y.shape}")
    print(f"📊 NaN count in X: {X.isnull().sum().sum()}")
    print(f"📊 NaN count in y: {y.isnull().sum()}")
    
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.25, random_state=42)
    
    saits_results = {}
    
    # Test SAITS model
    try:
        from saits_model_fixed import SAITSRegressor
        print("\n   Testing SAITS model...")
        
        model = SAITSRegressor(
            sequence_length=20,
            epochs=5,  # Reduced for testing
            batch_size=16,
            d_model=32,
            verbose=True
        )
        
        print("   🔧 Starting SAITS training...")
        model.fit(X_train, y_train, eval_set=[(X_val, y_val)], verbose=False)
        
        print("   🔮 Making SAITS predictions...")
        predictions = model.predict(X_val)
        
        # Check predictions - handle sequence length mismatch
        valid_preds = ~np.isnan(predictions)
        if np.any(valid_preds):
            from sklearn.metrics import mean_absolute_error, r2_score
            # Adjust for sequence length difference
            min_len = min(len(y_val), len(predictions))
            y_val_adj = y_val.iloc[:min_len] if hasattr(y_val, 'iloc') else y_val[:min_len]
            pred_adj = predictions[:min_len]
            valid_adj = ~np.isnan(pred_adj)

            if np.any(valid_adj):
                mae = mean_absolute_error(y_val_adj[valid_adj], pred_adj[valid_adj])
                r2 = r2_score(y_val_adj[valid_adj], pred_adj[valid_adj])
                print(f"   ✅ SAITS training successful: MAE={mae:.3f}, R²={r2:.3f}")
                saits_results['SAITS'] = {'success': True, 'mae': mae, 'r2': r2}
            else:
                print("   ❌ SAITS training failed: All adjusted predictions are NaN")
                saits_results['SAITS'] = {'success': False, 'error': 'All adjusted predictions NaN'}
        else:
            print("   ❌ SAITS training failed: All predictions are NaN")
            saits_results['SAITS'] = {'success': False, 'error': 'All predictions NaN'}
            
    except Exception as e:
        print(f"   ❌ SAITS training failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        saits_results['SAITS'] = {'success': False, 'error': str(e)}
    
    # Test Simple SAITS model
    try:
        from simple_saits import SimpleSAITSRegressor
        print("\n   Testing Simple SAITS model...")
        
        model = SimpleSAITSRegressor(
            sequence_length=15,
            epochs=5,  # Reduced for testing
            batch_size=16,
            d_model=32
        )
        
        print("   🔧 Starting Simple SAITS training...")
        model.fit(X_train, y_train, eval_set=[(X_val, y_val)], verbose=False)
        
        print("   🔮 Making Simple SAITS predictions...")
        predictions = model.predict(X_val)
        
        # Check predictions - handle sequence length mismatch
        valid_preds = ~np.isnan(predictions)
        if np.any(valid_preds):
            from sklearn.metrics import mean_absolute_error, r2_score
            # Adjust for sequence length difference
            min_len = min(len(y_val), len(predictions))
            y_val_adj = y_val.iloc[:min_len] if hasattr(y_val, 'iloc') else y_val[:min_len]
            pred_adj = predictions[:min_len]
            valid_adj = ~np.isnan(pred_adj)

            if np.any(valid_adj):
                mae = mean_absolute_error(y_val_adj[valid_adj], pred_adj[valid_adj])
                r2 = r2_score(y_val_adj[valid_adj], pred_adj[valid_adj])
                print(f"   ✅ Simple SAITS training successful: MAE={mae:.3f}, R²={r2:.3f}")
                saits_results['Simple SAITS'] = {'success': True, 'mae': mae, 'r2': r2}
            else:
                print("   ❌ Simple SAITS training failed: All adjusted predictions are NaN")
                saits_results['Simple SAITS'] = {'success': False, 'error': 'All adjusted predictions NaN'}
        else:
            print("   ❌ Simple SAITS training failed: All predictions are NaN")
            saits_results['Simple SAITS'] = {'success': False, 'error': 'All predictions NaN'}
            
    except Exception as e:
        print(f"   ❌ Simple SAITS training failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        saits_results['Simple SAITS'] = {'success': False, 'error': str(e)}
    
    return saits_results

def test_ml_core_integration(df):
    """Test the full ML core integration with SAITS models."""
    print("\n🧪 Testing ML Core Integration")
    print("-" * 40)
    
    try:
        from ml_core import impute_logs, MODEL_REGISTRY
        from config_handler import configure_hyperparameters
        
        # Prepare configuration
        feature_cols = ['GR', 'NPHI']
        target_col = 'DT'
        well_cfg = {'mode': 'combined'}
        prediction_mode = 1
        
        # Get hyperparameters
        hparams = configure_hyperparameters()
        
        # Create models - test with just SAITS models first
        saits_models = {}
        for model_key in ['saits', 'simple_saits']:
            if model_key in MODEL_REGISTRY:
                model_config = MODEL_REGISTRY[model_key]
                model_class = model_config['model_class']
                model_hparams = hparams[model_key].copy()
                
                # Reduce parameters for testing
                if 'epochs' in model_hparams:
                    model_hparams['epochs'] = 5
                if 'sequence_length' in model_hparams:
                    model_hparams['sequence_length'] = 15
                
                saits_models[model_config['name']] = model_class(**model_hparams)
        
        print(f"📋 Testing with SAITS models: {list(saits_models.keys())}")
        
        # Run imputation
        print("🔧 Running impute_logs with SAITS models...")
        result_df, model_results = impute_logs(df, feature_cols, target_col, saits_models, well_cfg, prediction_mode)
        
        if model_results:
            print("✅ ML Core integration successful!")
            print(f"   Best model: {model_results['best_model_name']}")
            print(f"   Evaluations: {len(model_results['evaluations'])}")
            
            for eval_result in model_results['evaluations']:
                print(f"   • {eval_result['model_name']}: MAE={eval_result['mae']:.3f}, R²={eval_result['r2']:.3f}")
            
            return True, model_results
        else:
            print("❌ ML Core integration failed: No model results returned")
            return False, None
            
    except Exception as e:
        print(f"❌ ML Core integration failed: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False, None

def main():
    """Run comprehensive SAITS pipeline diagnosis."""
    print("🚀 SAITS Pipeline Diagnosis")
    print("=" * 60)
    
    # Create test data
    print("📊 Creating test data...")
    df = create_test_data()
    print(f"✅ Test data created: {df.shape}")
    
    # Test 1: Model imports
    import_results = test_model_imports()
    
    # Test 2: Model registry
    registry_success, model_registry = test_model_registry()
    
    if not registry_success:
        print("❌ Cannot continue without MODEL_REGISTRY")
        return
    
    # Test 3: Model instantiation
    instantiation_results = test_model_instantiation(model_registry)
    
    # Test 4: SAITS training
    saits_training_results = test_saits_training(df)
    
    # Test 5: ML Core integration
    integration_success, integration_results = test_ml_core_integration(df)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSIS SUMMARY")
    print("=" * 60)
    
    print("\n🔍 Import Results:")
    for model, success in import_results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {model}")
    
    print("\n🔍 Instantiation Results:")
    for model, success in instantiation_results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {model}")
    
    print("\n🔍 SAITS Training Results:")
    for model, result in saits_training_results.items():
        if result['success']:
            print(f"   ✅ {model}: MAE={result['mae']:.3f}, R²={result['r2']:.3f}")
        else:
            print(f"   ❌ {model}: {result['error']}")
    
    print(f"\n🔍 ML Core Integration: {'✅ Success' if integration_success else '❌ Failed'}")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    
    saits_working = any(result['success'] for result in saits_training_results.values())
    
    if saits_working and integration_success:
        print("✅ SAITS models are working correctly!")
        print("   The issue may be in the plotting or results display logic.")
        print("   Next steps:")
        print("   1. Check the plotting functions")
        print("   2. Verify results are being stored correctly")
        print("   3. Run the full pipeline with debug output")
    elif saits_working and not integration_success:
        print("⚠️  SAITS models work individually but fail in ML Core integration")
        print("   Next steps:")
        print("   1. Check the impute_logs function")
        print("   2. Verify model evaluation logic")
        print("   3. Check for feature mismatch issues")
    else:
        print("❌ SAITS models are not working correctly")
        print("   Next steps:")
        print("   1. Check SAITS model implementations")
        print("   2. Verify dependencies are installed")
        print("   3. Check for data preprocessing issues")

if __name__ == "__main__":
    main()
