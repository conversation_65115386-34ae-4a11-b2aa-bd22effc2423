#!/usr/bin/env python3
"""
Test Full Pipeline with SAITS
=============================

This script tests the complete ML pipeline including SAITS models
to verify they appear in results and plots alongside traditional models.
"""

import numpy as np
import pandas as pd
from ml_core import impute_logs, MODEL_REGISTRY
from config_handler import configure_hyperparameters
from reporting import create_all_models_plots

def create_test_data():
    """Create realistic test data similar to log data."""
    np.random.seed(42)
    n_samples = 100  # Smaller dataset for faster testing
    
    # Create synthetic log data
    md = np.linspace(1000, 1500, n_samples)
    gr = 50 + 30 * np.sin(md / 100) + np.random.normal(0, 5, n_samples)
    dt = 2.3 + 0.2 * np.sin(md / 150) + np.random.normal(0, 0.1, n_samples)
    nphi = 0.15 + 0.1 * np.cos(md / 120) + np.random.normal(0, 0.02, n_samples)
    
    # Add some NaN values to simulate missing data
    nan_indices = np.random.choice(n_samples, size=int(0.1 * n_samples), replace=False)
    dt[nan_indices] = np.nan
    
    df = pd.DataFrame({
        'WELL': ['WELL_A'] * n_samples,
        'MD': md,
        'GR': gr,
        'DT': dt,
        'NPHI': nphi
    })
    
    return df

def test_full_pipeline():
    """Test the complete pipeline with all models including SAITS."""
    print("🚀 Testing Full ML Pipeline with SAITS")
    print("=" * 60)
    
    # Create test data
    print("📊 Creating test data...")
    df = create_test_data()
    print(f"✅ Test data created: {df.shape}")
    
    # Configuration
    feature_cols = ['GR', 'NPHI']
    target_col = 'DT'
    well_cfg = {'mode': 'combined'}
    prediction_mode = 1
    
    # Get hyperparameters
    print("🔧 Getting hyperparameters...")
    hparams = configure_hyperparameters()
    
    # Reduce training time for testing
    for model_key in hparams:
        if 'epochs' in hparams[model_key]:
            hparams[model_key]['epochs'] = 10  # Reduced for testing
        if 'n_estimators' in hparams[model_key]:
            hparams[model_key]['n_estimators'] = 50  # Reduced for testing
        if 'iterations' in hparams[model_key]:
            hparams[model_key]['iterations'] = 50  # Reduced for testing
    
    # Create all models
    print("🤖 Creating models...")
    models = {}
    for k in MODEL_REGISTRY:
        try:
            model_config = MODEL_REGISTRY[k]
            model_class = model_config['model_class']
            model_hparams = hparams[k]
            models[model_config['name']] = model_class(**model_hparams)
            print(f"   ✅ {model_config['name']} created")
        except Exception as e:
            print(f"   ❌ {model_config['name']} failed: {e}")
    
    print(f"\n📋 Testing with {len(models)} models: {list(models.keys())}")
    
    # Run the full imputation workflow
    print(f"\n🚀 Running imputation workflow...")
    result_df, model_results = impute_logs(df, feature_cols, target_col, models, well_cfg, prediction_mode)
    
    if model_results:
        print(f"\n🏆 Pipeline completed successfully!")
        print(f"   Best model: {model_results['best_model_name']}")
        print(f"   Total models evaluated: {len(model_results['evaluations'])}")
        
        # Show evaluation results
        print(f"\n📈 Model Performance Ranking:")
        for i, eval_result in enumerate(model_results['evaluations'], 1):
            print(f"   {i}. {eval_result['model_name']}: "
                  f"MAE={eval_result['mae']:.3f}, "
                  f"R²={eval_result['r2']:.3f}, "
                  f"Score={eval_result['composite_score']:.3f}")
        
        # Check if SAITS models are included
        saits_models = [eval_result['model_name'] for eval_result in model_results['evaluations'] 
                       if 'SAITS' in eval_result['model_name']]
        
        if saits_models:
            print(f"\n✅ SAITS models found in results: {saits_models}")
        else:
            print(f"\n❌ No SAITS models found in results")
        
        # Test plotting
        print(f"\n📊 Testing plotting functionality...")
        try:
            create_all_models_plots(result_df, model_results, well_cfg)
            print("✅ Plotting completed successfully")
        except Exception as e:
            print(f"❌ Plotting failed: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
        
        return True
    else:
        print("❌ Pipeline failed: No model results returned")
        return False

def main():
    """Run the full pipeline test."""
    success = test_full_pipeline()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS: SAITS models are now integrated in the ML pipeline!")
        print("✅ SAITS models should now appear in your main pipeline outputs")
        print("✅ Performance metrics are being calculated and ranked")
        print("✅ Plotting functions should include SAITS results")
        print("\n💡 Next steps:")
        print("1. Run your main ML pipeline")
        print("2. SAITS models should now appear in plots and rankings")
        print("3. Consider tuning SAITS hyperparameters for better performance")
    else:
        print("❌ FAILURE: Issues still remain in the pipeline")
        print("Please check the error messages above")

if __name__ == "__main__":
    main()
