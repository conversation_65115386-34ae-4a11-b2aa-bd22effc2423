import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from sklearn.metrics import mean_absolute_error, r2_score

def generate_qc_report(df, logs, cfg):
    print("\nCoverage:")
    cov = 1 - df[logs].isna().mean()
    for l,c in cov.items():
        print(f"  {l}: {c:.1%}")

def create_summary_plots(res_df, model_res, cfg):
    """Create summary plots showing only the best model (original behavior)."""
    tgt = model_res['target']
    wells = cfg['prediction_wells'] if cfg['mode']=='separated' else res_df['WELL'].unique()
    wells = wells[:6]
    fig, axes = plt.subplots(1,len(wells), figsize=(4*len(wells),8), sharey=True)
    if len(wells)==1:
        axes=[axes]
    for ax,w in zip(axes,wells):
        d = res_df[res_df['WELL']==w]
        ax.plot(d[tgt], d['MD'], 'k-', label='Orig')
        ax.plot(d[f"{tgt}_imputed"], d['MD'], 'r--', label='Imp')
        comp = d[[tgt,f"{tgt}_imputed"]].dropna()
        if not comp.empty:
            mae = mean_absolute_error(comp[tgt], comp[f"{tgt}_imputed"])
            r2 = r2_score(comp[tgt], comp[f"{tgt}_imputed"])
            ax.set_title(f"{w}\nMAE={mae:.2f} R2={r2:.2f}")
        else:
            ax.set_title(w)
        ax.invert_yaxis()
    axes[0].set_ylabel('MD')
    axes[0].legend()
    plt.tight_layout()
    plt.show()

def create_all_models_plots(res_df, model_res, cfg):
    """Create comprehensive plots showing predictions from all trained models."""
    if not model_res or 'trained_models' not in model_res:
        print("❌ No trained models available for plotting")
        return

    tgt = model_res['target']
    trained_models = model_res['trained_models']
    evaluations = model_res['evaluations']

    # Get wells to plot (limit to 6 for readability)
    wells = cfg['prediction_wells'] if cfg['mode']=='separated' else res_df['WELL'].unique()
    wells = wells[:6]

    # Prepare feature set for predictions - use the same features that were used during training
    feature_cols = model_res.get('feature_cols', [])
    if not feature_cols:
        # Fallback to auto-detection if feature_cols not available (for backward compatibility)
        print("⚠️  Warning: Using auto-detected features. This may cause feature mismatch errors.")
        feature_cols = [col for col in res_df.columns if col not in ['WELL', 'MD', tgt, f'{tgt}_imputed', f'{tgt}_pred', f'{tgt}_error']]

    feat_set = feature_cols + ['MD']
    print(f"📋 Using {len(feature_cols)} features for prediction: {feature_cols}")

    # Validate that all required features are available in the prediction data
    missing_features = [col for col in feat_set if col not in res_df.columns]
    if missing_features:
        print(f"❌ Error: Missing required features in prediction data: {missing_features}")
        print(f"Available columns: {list(res_df.columns)}")
        raise ValueError(f"Feature mismatch: Missing features {missing_features} in prediction data")

    print(f"✅ Feature validation passed: All {len(feat_set)} required features are available")

    # Generate predictions from all models
    print(f"\n📊 Generating predictions from {len(trained_models)} models...")
    model_predictions = {}

    for model_name, model in trained_models.items():
        try:
            print(f"   Generating predictions for {model_name}...")

            # Prepare prediction data
            if cfg['mode'] == 'separated':
                pred_mask = res_df['WELL'].isin(cfg['prediction_wells'])
            else:
                pred_mask = pd.Series(True, index=res_df.index)

            X_pred = res_df.loc[pred_mask, feat_set].apply(lambda c: c.fillna(c.mean()), axis=0)

            if not X_pred.empty:
                preds = model.predict(X_pred)
                full_pred = pd.Series(np.nan, index=res_df.index)

                # Handle SAITS models that may return different length predictions
                if hasattr(model, '__class__') and 'SAITS' in model.__class__.__name__:
                    # For SAITS models, handle sequence length mismatch
                    pred_indices = res_df.index[pred_mask]
                    min_len = min(len(pred_indices), len(preds))
                    if min_len > 0:
                        full_pred.loc[pred_indices[:min_len]] = preds[:min_len]
                else:
                    # Traditional ML models
                    full_pred.loc[pred_mask] = preds

                model_predictions[model_name] = full_pred
            else:
                model_predictions[model_name] = pd.Series(np.nan, index=res_df.index)

        except Exception as e:
            print(f"   ❌ Failed to generate predictions for {model_name}: {e}")
            model_predictions[model_name] = pd.Series(np.nan, index=res_df.index)

    # Create evaluation metrics lookup
    eval_lookup = {eval_data['model_name']: eval_data for eval_data in evaluations}

    # Create separate figures for each model
    n_wells = len(wells)

    # Color palette for different models
    model_names = list(model_predictions.keys())
    colors = plt.cm.Set1(np.linspace(0, 1, len(model_names)))
    color_map = {name: colors[i] for i, name in enumerate(model_names)}

    print(f"📊 Creating individual plots for {len(model_predictions)} models...")

    for model_name, predictions in model_predictions.items():
        print(f"   📈 Plotting {model_name}...")

        # Create figure for this model
        fig, axes = plt.subplots(1, n_wells, figsize=(4*n_wells, 8), sharey=True)

        # Handle single well case
        if n_wells == 1:
            axes = [axes]

        for well_idx, well in enumerate(wells):
            ax = axes[well_idx]

            # Get well data
            well_data = res_df[res_df['WELL'] == well].copy()
            well_predictions = predictions[well_data.index]

            # Plot original data
            ax.plot(well_data[tgt], well_data['MD'], 'k-', linewidth=2, label='Original', alpha=0.8)

            # Plot model predictions
            valid_pred_mask = ~np.isnan(well_predictions)
            if valid_pred_mask.any():
                ax.plot(well_predictions[valid_pred_mask],
                       well_data['MD'][valid_pred_mask],
                       color=color_map[model_name], linestyle='--', linewidth=2,
                       label=f'{model_name} Pred', alpha=0.8)

            # Calculate metrics for this well
            well_orig = well_data[tgt].dropna()
            well_pred = well_predictions[well_data[tgt].notna()]

            if len(well_orig) > 0 and len(well_pred) > 0 and not np.isnan(well_pred).all():
                # Remove NaN values for metric calculation
                valid_mask = ~np.isnan(well_pred)
                if valid_mask.any():
                    mae = mean_absolute_error(well_orig[valid_mask], well_pred[valid_mask])
                    r2 = r2_score(well_orig[valid_mask], well_pred[valid_mask])

                    # Get overall model performance
                    overall_metrics = eval_lookup.get(model_name, {})
                    overall_mae = overall_metrics.get('mae', np.nan)
                    overall_r2 = overall_metrics.get('r2', np.nan)

                    title = f"{well}\n"
                    title += f"Well: MAE={mae:.2f}, R²={r2:.2f}\n"
                    title += f"Overall: MAE={overall_mae:.3f}, R²={overall_r2:.3f}"
                else:
                    title = f"{well}\nNo valid predictions"
            else:
                title = f"{well}\nNo data for comparison"

            ax.set_title(title, fontsize=12)
            ax.invert_yaxis()
            ax.grid(True, alpha=0.3)

            # Set labels
            if well_idx == 0:
                ax.set_ylabel('MD (Measured Depth)', fontsize=12)
            ax.set_xlabel(f'{tgt} Log Value', fontsize=12)

            # Add legend to the first subplot
            if well_idx == 0:
                ax.legend(fontsize=10)

        # Set figure title with model name and ranking
        model_rank = next((i+1 for i, eval_data in enumerate(evaluations)
                          if eval_data['model_name'] == model_name), 'N/A')
        overall_metrics = eval_lookup.get(model_name, {})
        overall_mae = overall_metrics.get('mae', np.nan)
        overall_r2 = overall_metrics.get('r2', np.nan)

        fig.suptitle(f'{model_name} (Rank #{model_rank}) - Target: {tgt}\n'
                    f'Overall Performance: MAE={overall_mae:.3f}, R²={overall_r2:.3f}',
                    fontsize=14, fontweight='bold')

        plt.tight_layout()
        plt.subplots_adjust(top=0.85)
        plt.show()

        # Small pause between plots to ensure they display properly
        plt.pause(0.1)

    # Create a summary comparison plot
    create_model_comparison_summary(model_res)

    # Create a combined overview plot
    create_combined_overview_plot(res_df, model_res, cfg, model_predictions, eval_lookup)

def create_model_comparison_summary(model_res):
    """Create a summary bar chart comparing all model performances."""
    if not model_res or 'evaluations' not in model_res:
        return

    evaluations = model_res['evaluations']
    if not evaluations:
        return

    # Extract model names and metrics
    model_names = [eval_data['model_name'] for eval_data in evaluations]
    mae_scores = [eval_data['mae'] for eval_data in evaluations]
    r2_scores = [eval_data['r2'] for eval_data in evaluations]
    rmse_scores = [eval_data.get('rmse', np.nan) for eval_data in evaluations]

    # Create comparison plots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # MAE comparison
    bars1 = ax1.bar(model_names, mae_scores, color='skyblue', alpha=0.7)
    ax1.set_title('Mean Absolute Error (MAE) - Lower is Better', fontsize=12, fontweight='bold')
    ax1.set_ylabel('MAE')
    ax1.tick_params(axis='x', rotation=45)
    ax1.grid(True, alpha=0.3)

    # Add value labels on bars
    for bar, value in zip(bars1, mae_scores):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(mae_scores)*0.01,
                f'{value:.3f}', ha='center', va='bottom', fontsize=10)

    # R² comparison
    bars2 = ax2.bar(model_names, r2_scores, color='lightgreen', alpha=0.7)
    ax2.set_title('R² Score - Higher is Better', fontsize=12, fontweight='bold')
    ax2.set_ylabel('R² Score')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 1)

    # Add value labels on bars
    for bar, value in zip(bars2, r2_scores):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                f'{value:.3f}', ha='center', va='bottom', fontsize=10)

    # RMSE comparison (if available)
    if not all(np.isnan(rmse_scores)):
        bars3 = ax3.bar(model_names, rmse_scores, color='salmon', alpha=0.7)
        ax3.set_title('Root Mean Square Error (RMSE) - Lower is Better', fontsize=12, fontweight='bold')
        ax3.set_ylabel('RMSE')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar, value in zip(bars3, rmse_scores):
            if not np.isnan(value):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max([v for v in rmse_scores if not np.isnan(v)])*0.01,
                        f'{value:.3f}', ha='center', va='bottom', fontsize=10)
    else:
        ax3.text(0.5, 0.5, 'RMSE data not available', ha='center', va='center',
                transform=ax3.transAxes, fontsize=12)
        ax3.set_title('RMSE - Not Available', fontsize=12)

    # Model ranking
    sorted_evals = sorted(evaluations, key=lambda x: x.get('composite_score', float('inf')))
    ranking_names = [eval_data['model_name'] for eval_data in sorted_evals]
    ranking_scores = [eval_data.get('composite_score', 0) for eval_data in sorted_evals]

    bars4 = ax4.bar(ranking_names, ranking_scores, color='gold', alpha=0.7)
    ax4.set_title('Composite Score Ranking - Lower is Better', fontsize=12, fontweight='bold')
    ax4.set_ylabel('Composite Score')
    ax4.tick_params(axis='x', rotation=45)
    ax4.grid(True, alpha=0.3)

    # Add value labels and ranking
    for i, (bar, value) in enumerate(zip(bars4, ranking_scores)):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(ranking_scores)*0.01,
                f'#{i+1}\n{value:.3f}', ha='center', va='bottom', fontsize=10)

    plt.suptitle(f'Model Performance Comparison - Target: {model_res["target"]}',
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.show()

    # Print ranking summary
    print(f"\n🏆 Model Performance Ranking for {model_res['target']}:")
    print("=" * 60)
    for i, eval_data in enumerate(sorted_evals, 1):
        print(f"{i:2d}. {eval_data['model_name']:15s} - "
              f"MAE: {eval_data['mae']:.3f}, "
              f"R²: {eval_data['r2']:.3f}, "
              f"Composite: {eval_data.get('composite_score', 0):.3f}")

def create_combined_overview_plot(res_df, model_res, cfg, model_predictions, eval_lookup):
    """Create a combined overview plot showing all models on the same figure for comparison."""
    tgt = model_res['target']
    wells = cfg['prediction_wells'] if cfg['mode']=='separated' else res_df['WELL'].unique()
    wells = wells[:6]  # Limit to 6 wells for readability

    n_wells = len(wells)

    # Create figure with subplots for each well
    fig, axes = plt.subplots(1, n_wells, figsize=(5*n_wells, 8), sharey=True)

    # Handle single well case
    if n_wells == 1:
        axes = [axes]

    # Color palette for different models
    model_names = list(model_predictions.keys())
    colors = plt.cm.Set1(np.linspace(0, 1, len(model_names)))
    color_map = {name: colors[i] for i, name in enumerate(model_names)}

    print(f"   📈 Creating combined overview plot...")

    for well_idx, well in enumerate(wells):
        ax = axes[well_idx]

        # Get well data
        well_data = res_df[res_df['WELL'] == well].copy()

        # Plot original data
        ax.plot(well_data[tgt], well_data['MD'], 'k-', linewidth=3, label='Original', alpha=0.9)

        # Plot predictions from all models
        for model_name, predictions in model_predictions.items():
            well_predictions = predictions[well_data.index]

            # Plot model predictions
            valid_pred_mask = ~np.isnan(well_predictions)
            if valid_pred_mask.any():
                # Get model rank for line style
                model_rank = next((i+1 for i, eval_data in enumerate(model_res['evaluations'])
                                  if eval_data['model_name'] == model_name), len(model_res['evaluations']))

                # Use different line styles for different ranks
                if model_rank == 1:
                    linestyle = '-'  # Solid line for best model
                    linewidth = 2.5
                    alpha = 0.9
                elif model_rank <= 3:
                    linestyle = '--'  # Dashed for top 3
                    linewidth = 2
                    alpha = 0.8
                else:
                    linestyle = ':'  # Dotted for others
                    linewidth = 1.5
                    alpha = 0.7

                ax.plot(well_predictions[valid_pred_mask],
                       well_data['MD'][valid_pred_mask],
                       color=color_map[model_name], linestyle=linestyle,
                       linewidth=linewidth, label=f'{model_name} (#{model_rank})', alpha=alpha)

        # Calculate and display metrics for the best model in this well
        best_model_name = model_res['best_model_name']
        if best_model_name in model_predictions:
            best_predictions = model_predictions[best_model_name][well_data.index]
            well_orig = well_data[tgt].dropna()
            well_pred = best_predictions[well_data[tgt].notna()]

            if len(well_orig) > 0 and len(well_pred) > 0 and not np.isnan(well_pred).all():
                valid_mask = ~np.isnan(well_pred)
                if valid_mask.any():
                    mae = mean_absolute_error(well_orig[valid_mask], well_pred[valid_mask])
                    r2 = r2_score(well_orig[valid_mask], well_pred[valid_mask])
                    title = f"{well}\nBest Model ({best_model_name}): MAE={mae:.2f}, R²={r2:.2f}"
                else:
                    title = f"{well}\nNo valid predictions"
            else:
                title = f"{well}\nNo data for comparison"
        else:
            title = well

        ax.set_title(title, fontsize=12, fontweight='bold')
        ax.invert_yaxis()
        ax.grid(True, alpha=0.3)

        # Set labels
        if well_idx == 0:
            ax.set_ylabel('MD (Measured Depth)', fontsize=12)
        ax.set_xlabel(f'{tgt} Log Value', fontsize=12)

        # Add legend to the first subplot (limit to avoid overcrowding)
        if well_idx == 0:
            # Sort legend by model rank
            handles, labels = ax.get_legend_handles_labels()
            # Sort by rank (extract rank number from label)
            sorted_items = sorted(zip(handles, labels),
                                key=lambda x: int(x[1].split('#')[1].split(')')[0]) if '#' in x[1] else 999)
            sorted_handles, sorted_labels = zip(*sorted_items) if sorted_items else ([], [])
            ax.legend(sorted_handles, sorted_labels, fontsize=9, loc='best')

    # Set figure title
    fig.suptitle(f'All Models Combined Overview - Target: {tgt}\n'
                f'Line Styles: Solid (Best), Dashed (Top 3), Dotted (Others)',
                fontsize=14, fontweight='bold')

    plt.tight_layout()
    plt.subplots_adjust(top=0.85)
    plt.show()

def generate_final_report(model_res, hparams):
    print("\n=== FINAL REPORT ===")
    print(f"Target: {model_res['target']}")
    for i,e in enumerate(model_res['evaluations'],1):
        print(f"{i}. {e['model_name']} MAE={e['mae']:.3f} R2={e['r2']:.3f}")
