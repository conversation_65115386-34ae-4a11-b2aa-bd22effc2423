#!/usr/bin/env python3
"""
Test script to verify the endless validation fix.
"""

import numpy as np
import sys
import signal
import time

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully."""
    print('\n🛑 Test interrupted by user')
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)

def test_validation_limiting():
    """Test that validation messages are properly limited."""
    print("🧪 Testing validation message limiting...")
    
    try:
        from saits_model import validate_model_inputs
        import torch
        
        # Create test inputs
        X = torch.randn(16, 20, 4)
        mask = torch.ones_like(X)
        
        inputs = {
            "X": X,
            "missing_mask": mask
        }
        
        print("   Calling validate_model_inputs multiple times...")
        
        # Call validation function multiple times
        for i in range(15):
            validate_model_inputs(inputs)
            time.sleep(0.1)  # Small delay to see the effect
        
        print("   ✅ Validation limiting test completed")
        return True
        
    except Exception as e:
        print(f"   ❌ Validation test failed: {e}")
        return False

def test_simple_saits_with_timeout():
    """Test SimpleSAITSRegressor with a timeout to prevent endless loops."""
    print("\n🧪 Testing SimpleSAITSRegressor with timeout...")
    
    try:
        from simple_saits import SimpleSAITSRegressor
        
        # Create small test data to minimize computation
        np.random.seed(42)
        X = np.random.randn(50, 3)  # Small dataset
        y = np.random.randn(50)
        
        # Add some NaN values
        X[np.random.choice(50, 3), np.random.choice(3, 1)] = np.nan
        y[np.random.choice(50, 2)] = np.nan
        
        print(f"   Created test data: X{X.shape}, y{y.shape}")
        
        # Create model with very small parameters to reduce computation
        model = SimpleSAITSRegressor(
            sequence_length=10,  # Very short sequences
            d_model=16,          # Small model
            n_head=2,            # Few heads
            epochs=1,            # Just one epoch
            batch_size=8,        # Small batches
            patience=1,          # Quick early stopping
            device='cpu'         # Force CPU to avoid GPU issues
        )
        
        print("   Starting training with timeout...")
        
        # Set a timeout for training
        start_time = time.time()
        timeout = 30  # 30 seconds timeout
        
        try:
            model.fit(X, y, verbose=True)
            training_time = time.time() - start_time
            
            if training_time < timeout:
                print(f"   ✅ Training completed in {training_time:.1f} seconds")
                
                # Try prediction
                predictions = model.predict(X[:5])
                print(f"   ✅ Predictions shape: {predictions.shape}")
                return True
            else:
                print(f"   ⚠️  Training took too long ({training_time:.1f}s)")
                return False
                
        except Exception as e:
            training_time = time.time() - start_time
            print(f"   ❌ Training failed after {training_time:.1f}s: {e}")
            return False
        
    except Exception as e:
        print(f"   ❌ SimpleSAITS test failed: {e}")
        return False

def test_matrix_dimension_check():
    """Test if we can detect the matrix multiplication issue early."""
    print("\n🧪 Testing matrix dimension compatibility...")
    
    try:
        import torch
        import torch.nn as nn
        
        # Simulate the problematic dimensions from the error message
        # "mat1 and mat2 shapes cannot be multiplied (320x24 and 8x4)"
        
        print("   Simulating matrix multiplication error...")
        
        # This should fail
        try:
            mat1 = torch.randn(320, 24)
            mat2 = torch.randn(8, 4)
            result = torch.mm(mat1, mat2)
            print("   ❌ Expected error did not occur")
            return False
        except RuntimeError as e:
            if "cannot be multiplied" in str(e):
                print(f"   ✅ Correctly detected matrix multiplication error: {e}")
            else:
                print(f"   ⚠️  Different error: {e}")
        
        # This should work
        try:
            mat1 = torch.randn(320, 24)
            mat2 = torch.randn(24, 4)  # Fixed dimension
            result = torch.mm(mat1, mat2)
            print(f"   ✅ Correct matrix multiplication works: {result.shape}")
            return True
        except Exception as e:
            print(f"   ❌ Correct multiplication failed: {e}")
            return False
        
    except Exception as e:
        print(f"   ❌ Matrix dimension test failed: {e}")
        return False

def main():
    """Run all tests with timeout protection."""
    print("🚀 Testing Endless Validation Fix")
    print("=" * 50)
    print("⏰ Tests will timeout after 60 seconds to prevent endless loops")
    
    start_time = time.time()
    max_test_time = 60  # 1 minute total timeout
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Validation limiting
    if time.time() - start_time < max_test_time:
        if test_validation_limiting():
            tests_passed += 1
    
    # Test 2: Matrix dimension check
    if time.time() - start_time < max_test_time:
        if test_matrix_dimension_check():
            tests_passed += 1
    
    # Test 3: SimpleSAITS with timeout
    if time.time() - start_time < max_test_time:
        if test_simple_saits_with_timeout():
            tests_passed += 1
    
    total_time = time.time() - start_time
    
    print("\n" + "=" * 50)
    print(f"⏱️  Total test time: {total_time:.1f} seconds")
    print(f"✅ Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The endless validation issue should be fixed.")
        return 0
    else:
        print("⚠️  Some tests failed, but no endless loops detected.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
